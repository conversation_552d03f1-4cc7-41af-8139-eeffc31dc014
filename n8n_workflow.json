{"nodes": [{"parameters": {"model": "google/gemini-2.5-flash-preview-05-20:thinking", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [3020, 180], "id": "e516ddc3-1826-4af7-b2cf-efa23330417c", "name": "OpenRouter <PERSON>", "credentials": {"openRouterApi": {"id": "N9BoqTeHPsx7qRBt", "name": "OpenRouter account"}}}, {"parameters": {"operation": "get", "tableId": "documents", "filters": {"conditions": [{"keyName": "id", "keyValue": "=3c7f667a-e645-49d6-8b64-eb66706f3815"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [260, -760], "id": "bb9828bc-65e2-460a-a55d-777133b1de69", "name": "Get Document Content", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {"tableId": "lease_documents", "fieldsUi": {"fieldValues": [{"fieldId": "lease_term_duration", "fieldValue": "={{ $json.output.lease_term_duration }}"}, {"fieldId": "lease_term_years", "fieldValue": "={{ $json.output.lease_term_years }}"}, {"fieldId": "lease_term_months", "fieldValue": "={{ $json.output.lease_term_months }}"}, {"fieldId": "commencement_date", "fieldValue": "={{ $json.output.commencement_date }}"}, {"fieldId": "end_date", "fieldValue": "={{ $json.output.end_date }}"}, {"fieldId": "possession_date", "fieldValue": "={{ $json.output.possession_date }}"}, {"fieldId": "installation_period_months", "fieldValue": "={{ $json.output.installation_period_months }}"}, {"fieldId": "installation_period_start", "fieldValue": "={{ $json.output.installation_period_start }}"}, {"fieldId": "installation_period_end", "fieldValue": "={{ $json.output.installation_period_end }}"}, {"fieldId": "lease_status", "fieldValue": "={{ $json.output.lease_status }}"}, {"fieldId": "version_number", "fieldValue": "={{ $json.output.version_number }}"}, {"fieldId": "document_id", "fieldValue": "={{ $('Get Document Content').item.json.id }}"}, {"fieldId": "lease_execution_date", "fieldValue": "={{ $json.output.lease_execution_date }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1240, -760], "id": "eb1c7038-a7db-4d07-8bb2-ba9cbe7611b9", "name": "Create row in lease_documents table", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {"text": "={{ $json.complete_content }}", "attributes": {"attributes": [{"name": "lease_term_duration", "description": "the duration of the lease", "required": true}, {"name": "lease_term_years", "type": "number", "description": "the number of years for the term of the lease"}, {"name": "lease_term_months", "type": "number", "description": "the number of months for the term of the lease"}, {"name": "commencement_date", "type": "date", "description": "the starting date for the lease", "required": true}, {"name": "end_date", "type": "date", "description": "the end date for the lease", "required": true}, {"name": "possession_date", "type": "date", "description": "the possession date for the lease"}, {"name": "installation_period_months", "type": "number", "description": "the installation period in months"}, {"name": "installation_period_start", "type": "date", "description": "the starting date of the installation period"}, {"name": "installation_period_end", "type": "date", "description": "the end date of the installation period"}, {"name": "lease_status", "description": "the lease status", "required": true}, {"name": "version_number", "type": "number", "description": "the version number"}, {"name": "lease_execution_date", "type": "date", "description": "the lease execution date"}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [840, -760], "id": "2c48dde1-d3a2-4601-a3d9-c2c484fe5b61", "name": "Lease"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "attributes": {"attributes": [{"name": "party_name", "description": "name of the party", "required": true}, {"name": "legal_status", "description": "legal status of the party", "required": true}, {"name": "address_street", "description": "street address of the party"}, {"name": "address_city", "description": "city of the address of the party"}, {"name": "address_province", "description": "province of the address of the party"}, {"name": "address_postal_code", "description": "postal code of the address of the party"}, {"name": "representative_name", "description": "Name of the representative of the party"}, {"name": "representative_title", "description": "Title of the representative of the party"}, {"name": "authorization_details", "description": "Authorization details of the party"}, {"name": "email", "description": "the email of the party"}, {"name": "phone_number", "description": "the phone number of the party"}]}, "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value.\n\nDO IT FOR THE LANDLORD ONLY!"}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [1600, -1820], "id": "1edf4214-d3c8-4b8e-aa9b-185533873243", "name": "Parties: Landlord"}, {"parameters": {"tableId": "parties", "fieldsUi": {"fieldValues": [{"fieldId": "party_type", "fieldValue": "landlord"}, {"fieldId": "party_name", "fieldValue": "={{ $json.output.party_name }}"}, {"fieldId": "legal_status", "fieldValue": "={{ $json.output.legal_status }}"}, {"fieldId": "address_street", "fieldValue": "={{ $json.output.address_street }}"}, {"fieldId": "address_city", "fieldValue": "={{ $json.output.address_city }}"}, {"fieldId": "address_province", "fieldValue": "={{ $json.output.address_province }}"}, {"fieldId": "address_postal_code", "fieldValue": "={{ $json.output.address_postal_code }}"}, {"fieldId": "representative_name", "fieldValue": "={{ $json.output.representative_name }}"}, {"fieldId": "representative_title", "fieldValue": "={{ $json.output.representative_title }}"}, {"fieldId": "authorization_details", "fieldValue": "={{ $json.output.authorization_details }}"}, {"fieldId": "lease_id", "fieldValue": "={{ $('Create row in lease_documents table').item.json.lease_id }}"}, {"fieldId": "email", "fieldValue": "={{ $json.output.email }}"}, {"fieldId": "phone_number", "fieldValue": "={{ $json.output.phone_number }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1940, -1820], "id": "3da6a066-c3e0-47af-b362-4b5157ff50c0", "name": "Add Landlord to Parties Table", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {"tableId": "parties", "fieldsUi": {"fieldValues": [{"fieldId": "party_type", "fieldValue": "tenant"}, {"fieldId": "party_name", "fieldValue": "={{ $json.output.party_name }}"}, {"fieldId": "legal_status", "fieldValue": "={{ $json.output.legal_status }}"}, {"fieldId": "address_street", "fieldValue": "={{ $json.output.address_street }}"}, {"fieldId": "address_city", "fieldValue": "={{ $json.output.address_city }}"}, {"fieldId": "address_province", "fieldValue": "={{ $json.output.address_province }}"}, {"fieldId": "address_postal_code", "fieldValue": "={{ $json.output.address_postal_code }}"}, {"fieldId": "representative_name", "fieldValue": "={{ $json.output.representative_name }}"}, {"fieldId": "representative_title", "fieldValue": "={{ $json.output.representative_title }}"}, {"fieldId": "authorization_details", "fieldValue": "={{ $json.output.authorization_details }}"}, {"fieldId": "lease_id", "fieldValue": "={{ $('Create row in lease_documents table').item.json.lease_id }}"}, {"fieldId": "email", "fieldValue": "={{ $json.output.email }}"}, {"fieldId": "phone_number", "fieldValue": "={{ $json.output.phone_number }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1940, -2040], "id": "0a532eef-07b7-48d9-819e-fa0bb6f12f64", "name": "Add Tenant to Parties Table", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "attributes": {"attributes": [{"name": "leased_premises_address", "description": "the address of the leased premises", "required": true}, {"name": "building_address", "description": "the address of the building", "required": true}, {"name": "land_lot_number", "description": "the lot number of the land of the premise"}, {"name": "cadastre_details", "description": "the cadastre details of the land of the premise"}, {"name": "rental_area_sqft", "type": "number", "description": "The number of square ft of the rental area", "required": true}, {"name": "measurement_method", "description": "the method of measurement for the number of square ft.", "required": true}, {"name": "property_condition", "description": "the conditions of the property.", "required": true}, {"name": "plan_reference", "description": "the plan reference of the leased property."}, {"name": "physical_boundaries_description", "description": "the physical boundaries description"}]}, "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [1600, -1600], "id": "d460f988-1768-4122-b373-d58e6896288c", "name": "Properties"}, {"parameters": {"tableId": "properties", "fieldsUi": {"fieldValues": [{"fieldId": "lease_id", "fieldValue": "={{ $('Create row in lease_documents table').item.json.lease_id }}"}, {"fieldId": "leased_premises_address", "fieldValue": "={{ $json.output.leased_premises_address }}"}, {"fieldId": "building_address", "fieldValue": "={{ $json.output.building_address }}"}, {"fieldId": "land_lot_number", "fieldValue": "={{ $json.output.land_lot_number }}"}, {"fieldId": "cadastre_details", "fieldValue": "={{ $json.output.cadastre_details }}"}, {"fieldId": "rental_area_sqft", "fieldValue": "={{ $json.output.rental_area_sqft }}"}, {"fieldId": "measurement_method", "fieldValue": "={{ $json.output.measurement_method }}"}, {"fieldId": "property_condition", "fieldValue": "={{ $json.output.property_condition }}"}, {"fieldId": "plan_reference", "fieldValue": "={{ $json.output.plan_reference }}"}, {"fieldId": "physical_boundaries_description", "fieldValue": "={{ $json.output.physical_boundaries_description }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [2040, -1600], "id": "8273db84-b86b-430b-93e6-f3991dacb7bb", "name": "Add Property Details", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "attributes": {"attributes": [{"name": "annual_rent_per_sqft", "type": "number", "description": "annual rent per sqft (in $)", "required": true}, {"name": "annual_base_rent", "type": "number", "description": "the annual base rent", "required": true}, {"name": "monthly_base_rent", "type": "number", "description": "the monthly base rent", "required": true}, {"name": "rent_currency", "description": "the rent currency, if unknown, assume CDN", "required": true}, {"name": "rent_payment_frequency", "description": "the frequency of the rent payments.", "required": true}, {"name": "rent_due_day", "type": "number", "description": "the rent due day (the day of the month in number)", "required": true}, {"name": "rent_payment_terms", "description": "the rent payment terms"}, {"name": "gst_rate", "type": "number", "description": "the gst rate"}, {"name": "qst_rate", "type": "number", "description": "the qst rate"}, {"name": "late_payment_fee", "type": "number", "description": "the late payment fee"}, {"name": "late_payment_threshold", "type": "number", "description": "the late payment threshold"}, {"name": "interest_rate_formula", "description": "the interest rate formula"}, {"name": "assignment_admin_fee", "type": "number", "description": "assignment admin fee"}, {"name": "authorized_transfer_admin_fee", "type": "number", "description": "authorized transfer admin fee"}, {"name": "utilities_responsibility", "description": "the utilities responsibility"}, {"name": "admin_fee_on_operating_expenses_rate", "type": "number", "description": "the admin fee on operating expense rate"}, {"name": "admin_fee_on_taxes_rate", "type": "number", "description": "the admin fee on taxes rate"}, {"name": "local_improvement_tax_terms", "description": "the local improvement tax terms"}, {"name": "tax_payment_method", "description": "the tax payment method"}, {"name": "tenant_business_tax_reponsibility_details", "description": "the tenant business tax responsibility details"}, {"name": "security_deposit_conditions", "description": "the security deposit conditions"}]}, "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [1600, -1340], "id": "56a6ba9e-4e3d-4ad6-9648-07de125be72a", "name": "Financial Terms"}, {"parameters": {"tableId": "financial_terms", "fieldsUi": {"fieldValues": [{"fieldId": "lease_id", "fieldValue": "={{ $('Create row in lease_documents table').item.json.lease_id }}"}, {"fieldId": "annual_rent_per_sqft", "fieldValue": "={{ $json.output.annual_rent_per_sqft }}"}, {"fieldId": "annual_base_rent", "fieldValue": "={{ $json.output.annual_base_rent }}"}, {"fieldId": "monthly_base_rent", "fieldValue": "={{ $json.output.monthly_base_rent }}"}, {"fieldId": "rent_currency", "fieldValue": "={{ $json.output.rent_currency }}"}, {"fieldId": "rent_payment_frequency", "fieldValue": "={{ $json.output.rent_payment_frequency }}"}, {"fieldId": "rent_due_day", "fieldValue": "={{ $json.output.rent_due_day }}"}, {"fieldId": "rent_payment_terms", "fieldValue": "={{ $json.output.rent_payment_terms }}"}, {"fieldId": "gst_rate", "fieldValue": "={{ $json.output.gst_rate }}"}, {"fieldId": "qst_rate", "fieldValue": "={{ $json.output.qst_rate }}"}, {"fieldId": "late_payment_fee", "fieldValue": "={{ $json.output.late_payment_fee }}"}, {"fieldId": "late_payment_threshold", "fieldValue": "={{ $json.output.late_payment_threshold }}"}, {"fieldId": "interest_rate_formula", "fieldValue": "={{ $json.output.interest_rate_formula }}"}, {"fieldId": "assignment_admin_fee", "fieldValue": "={{ $json.output.assignment_admin_fee }}"}, {"fieldId": "authorized_transfer_admin_fee", "fieldValue": "={{ $json.output.authorized_transfer_admin_fee }}"}, {"fieldId": "utilities_responsibility", "fieldValue": "={{ $json.output.utilities_responsibility }}"}, {"fieldId": "admin_fee_on_operating_expenses_rate", "fieldValue": "={{ $json.output.admin_fee_on_operating_expenses_rate }}"}, {"fieldId": "admin_fee_on_taxes_rate", "fieldValue": "={{ $json.output.admin_fee_on_taxes_rate }}"}, {"fieldId": "local_improvement_tax_terms", "fieldValue": "={{ $json.output.local_improvement_tax_terms }}"}, {"fieldId": "tax_payment_method", "fieldValue": "={{ $json.output.tax_payment_method }}"}, {"fieldId": "tenant_business_tax_responsibility_details", "fieldValue": "={{ $json.output.tenant_business_tax_responsibility_details }}"}, {"fieldId": "utility_cost_recovery_mechanisms", "fieldValue": "={{ $json.output.utility_cost_recovery_mechanisms }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [2040, -1340], "id": "1a8742f3-581f-4c89-8498-cbe41f9b221e", "name": "Add Financial Terms", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "attributes": {"attributes": [{"name": "security_deposit_base", "type": "number", "description": "the security deposit base", "required": true}, {"name": "security_deposit_taxes", "type": "number", "description": "the security deposit taxes", "required": true}, {"name": "security_deposit_total", "type": "number", "description": "the security deposit total", "required": true}, {"name": "security_deposit_payment_method", "description": "the security deposit payment method.", "required": true}, {"name": "security_deposit_interest_accrual_terms", "description": "the security deposit interest accrual terms"}, {"name": "security_deposit_conditions", "description": "the security deposit conditions"}]}, "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [1600, -1100], "id": "bac139c2-b55c-49de-8ac0-9f11f87a3a1f", "name": "Security Deposits"}, {"parameters": {"tableId": "security_deposits", "fieldsUi": {"fieldValues": [{"fieldId": "lease_id", "fieldValue": "={{ $('Create row in lease_documents table').item.json.lease_id }}"}, {"fieldId": "security_deposit_base", "fieldValue": "={{ $json.output.security_deposit_base }}"}, {"fieldId": "security_deposit_taxes", "fieldValue": "={{ $json.output.security_deposit_taxes }}"}, {"fieldId": "security_deposit_total", "fieldValue": "={{ $json.output.security_deposit_total }}"}, {"fieldId": "security_deposit_payment_method", "fieldValue": "={{ $json.output.security_deposit_payment_method }}"}, {"fieldId": "security_deposit_interest_accrual_terms", "fieldValue": "={{ $json.output.security_deposit_interest_accrual_terms }}"}, {"fieldId": "security_deposit_conditions", "fieldValue": "={{ $json.output.security_deposit_conditions }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [2040, -1100], "id": "dabe27d6-3266-42f1-9b82-be17b15e3797", "name": "Add Security Depost", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "attributes": {"attributes": [{"name": "tacit_renewal_excluded", "type": "boolean", "description": "is the tacit renewal excluded", "required": true}, {"name": "holdover_rent_percentage", "type": "number", "description": "the holdover rent percentage", "required": true}, {"name": "holder_terms", "description": "the holdover terms", "required": true}, {"name": "property_return_condition", "description": "the property return condition", "required": true}, {"name": "removal_obligations", "description": "the removal obligations"}]}, "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [1600, -860], "id": "6c31481c-119a-4e1a-ad8e-d28ecb2d3141", "name": "Expiration Holdover Terms"}, {"parameters": {"tableId": "expiration_holdover_terms", "fieldsUi": {"fieldValues": [{"fieldId": "lease_id", "fieldValue": "={{ $('Create row in lease_documents table').item.json.lease_id }}"}, {"fieldId": "tacit_renewal_excluded", "fieldValue": "={{ $json.output.tacit_renewal_excluded }}"}, {"fieldId": "holdover_rent_percentage", "fieldValue": "={{ $json.output.holdover_rent_percentage }}"}, {"fieldId": "holdover_terms", "fieldValue": "={{ $json.output.holder_terms }}"}, {"fieldId": "property_return_condition", "fieldValue": "={{ $json.output.property_return_condition }}"}, {"fieldId": "removal_obligations", "fieldValue": "={{ $json.output.removal_obligations }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [2040, -860], "id": "96fbe6d7-f3a3-4c59-a890-145d26e2a99e", "name": "Add Expiration Holdover Terms", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {"fieldToSplitOut": "output.use_restrictions", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [2740, -1600], "id": "bef8e140-911c-44e4-8cf7-7fe73a50e93c", "name": "Split Out"}, {"parameters": {"tableId": "use_restrictions", "fieldsUi": {"fieldValues": [{"fieldId": "lease_id", "fieldValue": "={{ $('Create row in lease_documents table').first().json.lease_id }}"}, {"fieldId": "restriction_type", "fieldValue": "={{ $json.restriction_type }}"}, {"fieldId": "restriction_description", "fieldValue": "={{ $json.restriction_description }}"}, {"fieldId": "restriction_category", "fieldValue": "={{ $json.restriction_category }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [2960, -1600], "id": "861caeda-80f4-487a-b456-1976844e45fc", "name": "Add Restriction", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [3160, -1600], "id": "8e1eae05-9f6f-4777-82f0-8709b029b686", "name": "Limit"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"use_restrictions\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"restriction_type\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"permitted\",\n              \"prohibited\", \n              \"zoning\",\n              \"exterior\"\n            ]\n          },\n          \"restriction_description\": {\n            \"type\": \"string\"\n          },\n          \"restriction_category\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"use\",\n              \"activity\",\n              \"storage\",\n              \"vehicle\"\n            ]\n          }\n        },\n        \"required\": [\n          \"restriction_type\",\n          \"restriction_description\", \n          \"restriction_category\"\n        ]\n      }\n    }\n  },\n  \"required\": [\"use_restrictions\"]\n}", "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [2380, -1600], "id": "1cb74052-5457-4829-b4a4-3a1f5a17a99c", "name": "Restrictions (Multiples)"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"maintenance_obligations\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n\n          \"responsible_party\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"tenant\",\n              \"landlord\",\n              \"shared\"\n            ]\n          },\n          \"maintenance_type\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"general\",\n              \"hvac\",\n              \"structural\",\n              \"exterior\",\n              \"emergency\"\n            ]\n          },\n          \"maintenance_description\": {\n            \"type\": \"string\"\n          },\n          \"notification_requirements\": {\n            \"type\": \"string\"\n          },\n          \"cost_responsibility\": {\n            \"type\": \"string\"\n          }\n        },\n        \"required\": [\n          \"responsible_party\",\n          \"maintenance_type\",\n          \"maintenance_description\",\n          \"notification_requirements\",\n          \"cost_responsibility\"\n        ]\n      }\n    }\n  },\n  \"required\": [\"maintenance_obligations\"]\n}", "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [2380, -1320], "id": "5ac0de74-e463-42ca-bf4a-20199db4022b", "name": "Maintenance Obligations (Multiples)"}, {"parameters": {"tableId": "maintenance_obligations", "fieldsUi": {"fieldValues": [{"fieldId": "lease_id", "fieldValue": "={{ $('Create row in lease_documents table').first().json.lease_id }}"}, {"fieldId": "responsible_party", "fieldValue": "={{ $json.responsible_party }}"}, {"fieldId": "maintenance_type", "fieldValue": "={{ $json.maintenance_type }}"}, {"fieldId": "maintenance_description", "fieldValue": "={{ $json.maintenance_description }}"}, {"fieldId": "notification_requirements", "fieldValue": "={{ $json.notification_requirements }}"}, {"fieldId": "cost_responsibility", "fieldValue": "={{ $json.cost_responsibility }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [2960, -1320], "id": "2796f7f8-1b8a-4ecf-b23e-9cce8cb636eb", "name": "Add Maintenance Obligations", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {"fieldToSplitOut": "output.maintenance_obligations", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [2740, -1320], "id": "8485d06b-3c54-40f0-ae5c-079d036843ab", "name": "Split Out1"}, {"parameters": {}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [3160, -1320], "id": "d4edeb00-e3a6-4824-8658-a5fb01d63bf2", "name": "Limit1"}, {"parameters": {"fieldToSplitOut": "output.improvements", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [2740, -1060], "id": "1bec55e5-9fe4-492e-bc54-da32cbb44771", "name": "Split Out2"}, {"parameters": {}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [3160, -1060], "id": "9472e8f4-dc43-4f4e-9d0a-1ead5e1923b2", "name": "Limit2"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"improvements\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"improvement_type\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"tenant_work\",\n              \"landlord_work\",\n              \"approval_required\"\n            ]\n          },\n          \"cost_responsibility\": {\n            \"type\": \"string\"\n          },\n          \"admin_fee_percentage\": {\n            \"type\": \"number\"\n          },\n          \"quality_requirements\": {\n            \"type\": \"string\"\n          },\n          \"approval_required\": {\n            \"type\": \"boolean\"\n          },\n          \"contractor_restrictions\": {\n            \"type\": \"string\"\n          },\n          \"ownership_terms\": {\n            \"type\": \"string\"\n          }\n        },\n        \"required\": [\n          \"improvement_type\",\n          \"cost_responsibility\",\n          \"admin_fee_percentage\",\n          \"quality_requirements\",\n          \"approval_required\",\n          \"contractor_restrictions\",\n          \"ownership_terms\"\n        ]\n      }\n    }\n  },\n  \"required\": [\"improvements\"]\n}", "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [2380, -1060], "id": "01c6fa57-709c-4280-9684-18b1f4748b3d", "name": "Improvement Terms (Multiples)"}, {"parameters": {"tableId": "improvement_terms", "fieldsUi": {"fieldValues": [{"fieldId": "lease_id", "fieldValue": "={{ $('Create row in lease_documents table').first().json.lease_id }}"}, {"fieldId": "improvement_type", "fieldValue": "={{ $json.improvement_type }}"}, {"fieldId": "cost_responsibility", "fieldValue": "={{ $json.cost_responsibility }}"}, {"fieldId": "admin_fee_percentage", "fieldValue": "={{ $json.admin_fee_percentage }}"}, {"fieldId": "quality_requirements", "fieldValue": "={{ $json.quality_requirements }}"}, {"fieldId": "approval_required", "fieldValue": "={{ $json.approval_required }}"}, {"fieldId": "contractor_restrictions", "fieldValue": "={{ $json.contractor_restrictions }}"}, {"fieldId": "ownership_terms", "fieldValue": "={{ $json.ownership_terms }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [2960, -1060], "id": "444cebeb-e8d4-4519-a708-2947768eb13e", "name": "Add Improvement Terms", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {"fieldToSplitOut": "output.access_rights", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [2740, -820], "id": "b54b8a64-14b9-467a-a0f3-836dd39e886b", "name": "Split Out3"}, {"parameters": {}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [3160, -820], "id": "1c223aae-e0a2-44ca-8492-aadf13532072", "name": "Limit3"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"access_rights\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"access_type\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"inspection\",\n              \"emergency\",\n              \"showing\",\n              \"repairs\"\n            ]\n          },\n          \"notice_requirements\": {\n            \"type\": \"string\"\n          },\n          \"access_rights_description\": {\n            \"type\": \"string\"\n          },\n          \"time_restrictions\": {\n            \"type\": \"string\"\n          }\n        },\n        \"required\": [\n          \"access_type\",\n          \"notice_requirements\",\n          \"access_rights_description\",\n          \"time_restrictions\"\n        ]\n      }\n    }\n  },\n  \"required\": [\"access_rights\"]\n}", "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [2380, -820], "id": "6ac4487c-c80d-4c0d-81ca-2541e06d1a84", "name": "Access Inspection Rights (Multiples)"}, {"parameters": {"tableId": "access_inspection_rights", "fieldsUi": {"fieldValues": [{"fieldId": "lease_id", "fieldValue": "={{ $('Create row in lease_documents table').first().json.lease_id }}"}, {"fieldId": "access_type", "fieldValue": "={{ $json.access_type }}"}, {"fieldId": "notice_requirements", "fieldValue": "={{ $json.notice_requirements }}"}, {"fieldId": "access_rights_description", "fieldValue": "={{ $json.access_rights_description }}"}, {"fieldId": "time_restrictions", "fieldValue": "={{ $json.time_restrictions }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [2960, -820], "id": "87e2f910-34d4-49b5-8d13-6ad678a3e06a", "name": "Add Access Inspection Rights", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {"fieldToSplitOut": "output.compliance_requirements", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [2740, -600], "id": "d63f764a-889b-48a8-8ce8-8bd6151f2c92", "name": "Split Out4"}, {"parameters": {}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [3160, -600], "id": "3c527e99-0814-4d0a-b234-8e16a0af01d2", "name": "Limit4"}, {"parameters": {"fieldToSplitOut": "output.signage", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [2740, -380], "id": "0a85117d-9afc-41e5-8d4f-8d994e8cec07", "name": "Split Out5"}, {"parameters": {}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [3160, -380], "id": "c6113987-72af-41ad-b629-aa4fd077fa71", "name": "Limit5"}, {"parameters": {"fieldToSplitOut": "output.transfer_rights", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [5060, -1580], "id": "6e72de66-192e-4751-abc3-7af62a5de196", "name": "Split Out6"}, {"parameters": {}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [5480, -1580], "id": "8d4aa303-74b8-4a56-9e3b-dfec5c50b6f2", "name": "Limit6"}, {"parameters": {"fieldToSplitOut": "output.default_remedies", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [5060, -1300], "id": "e57bbbb0-ee6d-479f-951b-38a27145813a", "name": "Split Out7"}, {"parameters": {}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [5480, -1300], "id": "561c4f54-a84f-4131-92d8-9a4bded7ea40", "name": "Limit7"}, {"parameters": {"fieldToSplitOut": "output.liability_provisions", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [5060, -1060], "id": "40c64d9e-d493-46ab-8555-a5c94cbcc204", "name": "Split Out8"}, {"parameters": {}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [5480, -1060], "id": "e5732152-4ff3-4a13-9566-87f1ff1ca2c5", "name": "Limit8"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"compliance_requirements\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"compliance_type\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"fire_safety\",\n              \"health\",\n              \"environmental\",\n              \"municipal\",\n              \"insurance\"\n            ]\n          },\n          \"compliance_description\": {\n            \"type\": \"string\"\n          },\n          \"responsibility_party\": {\n            \"type\": \"string\"\n          },\n          \"penalty_terms\": {\n            \"type\": \"string\"\n          }\n        },\n        \"required\": [\n          \"compliance_type\",\n          \"compliance_description\",\n          \"responsibility_party\",\n          \"penalty_terms\"\n        ]\n      }\n    }\n  },\n  \"required\": [\"compliance_requirements\"]\n}", "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [2380, -600], "id": "367d2c92-1973-4baa-a5e1-a5cb1065ea78", "name": "Compliance Requirements (Multiples)"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"signage\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"signage_type\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"tenant_signs\",\n              \"landlord_signs\",\n              \"restrictions\"\n            ]\n          },\n          \"approval_required\": {\n            \"type\": \"boolean\"\n          },\n          \"signage_restrictions\": {\n            \"type\": \"string\"\n          },\n          \"cost_responsibility\": {\n            \"type\": \"string\"\n          },\n          \"removal_requirements\": {\n            \"type\": \"string\"\n          }\n        },\n        \"required\": [\n          \"signage_type\",\n          \"approval_required\",\n          \"signage_restrictions\",\n          \"cost_responsibility\",\n          \"removal_requirements\"\n        ]\n      }\n    }\n  },\n  \"required\": [\"signage\"]\n}", "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [2380, -380], "id": "dabca36b-1e3d-4aa5-8c09-5d9775546b31", "name": "Signage Provisions (Multiples)"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"transfer_rights\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"transfer_type\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"assignment\",\n              \"subletting\",\n              \"authorized_transfer\"\n            ]\n          },\n          \"consent_required\": {\n            \"type\": \"boolean\"\n          },\n          \"refusal_reasons\": {\n            \"type\": \"string\"\n          },\n          \"response_time_days\": {\n            \"type\": \"integer\"\n          },\n          \"information_requirements\": {\n            \"type\": \"string\"\n          },\n          \"admin_fee\": {\n            \"type\": \"number\"\n          },\n          \"landlord_recapture_right\": {\n            \"type\": \"boolean\"\n          }\n        },\n        \"required\": [\n          \"transfer_type\",\n          \"consent_required\",\n          \"refusal_reasons\",\n          \"response_time_days\",\n          \"information_requirements\",\n          \"admin_fee\",\n          \"landlord_recapture_right\"\n        ]\n      }\n    }\n  },\n  \"required\": [\"transfer_rights\"]\n}", "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [4700, -1580], "id": "b43b9bee-e468-4c52-993f-2108a416f8ad", "name": "Assignment Subletting Terms (Multiples)"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"default_remedies\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"default_type\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"monetary\",\n              \"non_monetary\",\n              \"bankruptcy\",\n              \"liens\"\n            ]\n          },\n          \"cure_period_days\": {\n            \"type\": \"integer\"\n          },\n          \"default_description\": {\n            \"type\": \"string\"\n          },\n          \"remedy_description\": {\n            \"type\": \"string\"\n          },\n          \"accelerated_rent_months\": {\n            \"type\": \"integer\"\n          },\n          \"interest_calculation\": {\n            \"type\": \"string\"\n          }\n        },\n        \"required\": [\n          \"default_type\",\n          \"cure_period_days\",\n          \"default_description\",\n          \"remedy_description\",\n          \"accelerated_rent_months\",\n          \"interest_calculation\"\n        ]\n      }\n    }\n  },\n  \"required\": [\"default_remedies\"]\n}", "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [4700, -1300], "id": "be7b03c6-9c7e-477b-8092-c982a519c881", "name": "<PERSON><PERSON><PERSON> (Multiples)"}, {"parameters": {"fieldToSplitOut": "output.force_majeure", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [5060, -860], "id": "af1b5c45-4e07-4f4f-adf9-9f309eb86655", "name": "Split Out9"}, {"parameters": {}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [5480, -860], "id": "9d34ec6b-6192-4efc-a01a-6512c3c7fea5", "name": "Limit9"}, {"parameters": {"fieldToSplitOut": "output.extraordinary_events", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [5060, -620], "id": "dab7e5a4-e823-4ce4-ad91-e9eebfb80e34", "name": "Split Out10"}, {"parameters": {}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [5480, -620], "id": "26f59da6-7a81-4e4c-b1f0-5c2f27c3dfa0", "name": "Limit10"}, {"parameters": {"fieldToSplitOut": "output.notices_service", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [5040, -380], "id": "eb60a0b5-fb93-440a-87e8-93bc7088ef6d", "name": "Split Out11"}, {"parameters": {}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [5460, -380], "id": "c293cf6f-30bb-4bc5-a7ba-8873c67dc668", "name": "Limit11"}, {"parameters": {"fieldToSplitOut": "output.legal_provisions", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [6160, -1580], "id": "f45b2c94-71a4-4770-b1ea-e64454d4c476", "name": "Split Out12"}, {"parameters": {}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [6580, -1580], "id": "2f5af48d-3a9c-4f23-b496-d96e92147acd", "name": "Limit12"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"liability_provisions\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"provision_type\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"liability_limitation\",\n              \"indemnification\",\n              \"insurance_requirement\"\n            ]\n          },\n          \"responsible_party\": {\n            \"type\": \"string\"\n          },\n          \"provision_description\": {\n            \"type\": \"string\"\n          },\n          \"exclusions\": {\n            \"type\": \"string\"\n          }\n        },\n        \"required\": [\n          \"provision_type\",\n          \"responsible_party\",\n          \"provision_description\",\n          \"exclusions\"\n        ]\n      }\n    }\n  },\n  \"required\": [\"liability_provisions\"]\n}", "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [4700, -1060], "id": "55697268-4165-4ad5-bbdc-653949137ef7", "name": "Insurance Liability Terms (Multiples)"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"force_majeure\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"event_type\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"unavoidable_delay\",\n              \"health_emergency\",\n              \"natural_disaster\"\n            ]\n          },\n          \"event_description\": {\n            \"type\": \"string\"\n          },\n          \"effect_on_obligations\": {\n            \"type\": \"string\"\n          },\n          \"rent_payment_exemption\": {\n            \"type\": \"boolean\"\n          }\n        },\n        \"required\": [\n          \"event_type\",\n          \"event_description\",\n          \"effect_on_obligations\",\n          \"rent_payment_exemption\"\n        ]\n      }\n    }\n  },\n  \"required\": [\"force_majeure\"]\n}", "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [4700, -860], "id": "5e099724-af2a-458e-aa51-859b2fbddcff", "name": "Force Majeure Provisions (Multiples)"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"extraordinary_events\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"event_type\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"destruction\",\n              \"expropriation\",\n              \"relocation\"\n            ]\n          },\n          \"repair_timeline_days\": {\n            \"type\": \"integer\"\n          },\n          \"termination_rights\": {\n            \"type\": \"string\"\n          },\n          \"rent_abatement_provisions\": {\n            \"type\": \"string\"\n          },\n          \"cost_responsibility\": {\n            \"type\": \"string\"\n          }\n        },\n        \"required\": [\n          \"event_type\",\n          \"repair_timeline_days\",\n          \"termination_rights\",\n          \"rent_abatement_provisions\",\n          \"cost_responsibility\"\n        ]\n      }\n    }\n  },\n  \"required\": [\"extraordinary_events\"]\n}", "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [4700, -620], "id": "f292541f-9c57-487f-b6af-e647fc1bd18b", "name": "Destruction Expropriation Terms (Multiples)"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"notices_service\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"party_type\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"tenant\",\n              \"landlord\"\n            ]\n          },\n          \"notice_address\": {\n            \"type\": \"string\"\n          },\n          \"delivery_methods\": {\n            \"type\": \"string\"\n          },\n          \"service_domicile\": {\n            \"type\": \"string\"\n          }\n        },\n        \"required\": [\n          \"party_type\",\n          \"notice_address\",\n          \"delivery_methods\",\n          \"service_domicile\"\n        ]\n      }\n    }\n  },\n  \"required\": [\"notices_service\"]\n}", "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [4680, -380], "id": "753e531d-a0f6-47e9-85cd-082f6286ad7b", "name": "Notice Provisions (Multiples)"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"legal_provisions\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"provision_type\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"governing_law\",\n              \"waived_articles\",\n              \"severability\",\n              \"amendments\"\n            ]\n          },\n          \"provision_description\": {\n            \"type\": \"string\"\n          },\n          \"jurisdiction\": {\n            \"type\": \"string\"\n          },\n          \"requirements\": {\n            \"type\": \"string\"\n          }\n        },\n        \"required\": [\n          \"provision_type\",\n          \"provision_description\",\n          \"jurisdiction\",\n          \"requirements\"\n        ]\n      }\n    }\n  },\n  \"required\": [\"legal_provisions\"]\n}", "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [5800, -1580], "id": "3537a2df-8637-4de8-9b6e-4382c6677f55", "name": "Legal General Provisions (Multiples)"}, {"parameters": {"fieldToSplitOut": "output.landlord_protections", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [6160, -1300], "id": "dccaa71c-1a24-4847-aa63-21aed9cee765", "name": "Split Out13"}, {"parameters": {}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [6580, -1300], "id": "e201acc4-014a-4604-8e70-16bffe46ea45", "name": "Limit13"}, {"parameters": {"fieldToSplitOut": "output.signatures", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [6140, -1060], "id": "7142141d-b0d0-40ad-84a6-e7b7bad9da87", "name": "Split Out14"}, {"parameters": {}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [6560, -1060], "id": "6bd7cd11-d685-40fe-a7a9-0442bdf63f8a", "name": "Limit14"}, {"parameters": {"fieldToSplitOut": "output.attachments", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [6140, -820], "id": "8570f637-1e0e-4a0c-8d28-2582d40afc23", "name": "Split Out15"}, {"parameters": {}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [6560, -820], "id": "73a7545d-e7f1-46a3-8567-6adef7298eec", "name": "Limit15"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"landlord_protections\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"provision_type\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"definition\",\n              \"landlord_rights\",\n              \"liability_exclusions\"\n            ]\n          },\n          \"provision_description\": {\n            \"type\": \"string\"\n          },\n          \"landlord_rights\": {\n            \"type\": \"string\"\n          },\n          \"liability_exclusions\": {\n            \"type\": \"string\"\n          }\n        },\n        \"required\": [\n          \"provision_type\",\n          \"provision_description\",\n          \"landlord_rights\",\n          \"liability_exclusions\"\n        ]\n      }\n    }\n  },\n  \"required\": [\"landlord_protections\"]\n}", "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [5800, -1300], "id": "816016d3-6b42-4a11-ba74-2ee5b977d28c", "name": "Health Emergency Provisions (Multiples)"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"signatures\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"party_type\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"landlord\",\n              \"tenant\"\n            ]\n          },\n          \"signatory_name\": {\n            \"type\": \"string\"\n          },\n          \"signatory_title\": {\n            \"type\": \"string\"\n          },\n          \"execution_date\": {\n            \"type\": \"string\",\n            \"format\": \"date\"\n          }\n        },\n        \"required\": [\n          \"party_type\",\n          \"signatory_name\",\n          \"signatory_title\",\n          \"execution_date\"\n        ]\n      }\n    }\n  },\n  \"required\": [\"signatures\"]\n}", "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [5780, -1060], "id": "f2749ccb-5862-4672-bdb0-e187dd6756b6", "name": "Signatures (Multiples)"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"attachments\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"attachment_type\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"plan\",\n              \"resolutions\",\n              \"other\"\n            ]\n          },\n          \"attachment_name\": {\n            \"type\": \"string\"\n          },\n          \"attachment_description\": {\n            \"type\": \"string\"\n          },\n          \"annex_reference\": {\n            \"type\": \"string\"\n          }\n        },\n        \"required\": [\n          \"attachment_type\",\n          \"attachment_name\",\n          \"attachment_description\",\n          \"annex_reference\"\n        ]\n      }\n    }\n  },\n  \"required\": [\"attachments\"]\n}", "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [5780, -820], "id": "0011d419-2d72-477a-bbe8-86dc992b26a0", "name": "Document Attachments (Multiples)"}, {"parameters": {"tableId": "compliance_requirements", "fieldsUi": {"fieldValues": [{"fieldId": "lease_id", "fieldValue": "={{ $('Create row in lease_documents table').first().json.lease_id }}"}, {"fieldId": "compliance_type", "fieldValue": "={{ $json.compliance_type }}"}, {"fieldId": "compliance_description", "fieldValue": "={{ $json.compliance_description }}"}, {"fieldId": "responsibility_party", "fieldValue": "={{ $json.responsibility_party }}"}, {"fieldId": "penalty_terms", "fieldValue": "={{ $json.penalty_terms }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [2960, -600], "id": "a309b890-37ac-4213-9242-2da48d501bff", "name": "Add Compliance Requirements", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {"tableId": "signage_provisions", "fieldsUi": {"fieldValues": [{"fieldId": "lease_id", "fieldValue": "={{ $('Create row in lease_documents table').first().json.lease_id }}"}, {"fieldId": "signage_type", "fieldValue": "={{ $json.signage_type }}"}, {"fieldId": "approval_required", "fieldValue": "={{ $json.approval_required }}"}, {"fieldId": "signage_restrictions", "fieldValue": "={{ $json.signage_restrictions }}"}, {"fieldId": "cost_responsibility", "fieldValue": "={{ $json.cost_responsibility }}"}, {"fieldId": "removal_requirements", "fieldValue": "={{ $json.removal_requirements }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [2960, -380], "id": "e2608fbe-cb94-4642-8dd9-d98d7a72d1f5", "name": "Add Signage Provisions", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {"tableId": "assignment_subletting_terms", "fieldsUi": {"fieldValues": [{"fieldId": "lease_id", "fieldValue": "={{ $('Create row in lease_documents table').first().json.lease_id }}"}, {"fieldId": "transfer_type", "fieldValue": "={{ $json.transfer_type }}"}, {"fieldId": "consent_required", "fieldValue": "={{ $json.consent_required }}"}, {"fieldId": "refusal_reasons", "fieldValue": "={{ $json.refusal_reasons }}"}, {"fieldId": "response_time_days", "fieldValue": "={{ $json.response_time_days }}"}, {"fieldId": "information_requirements", "fieldValue": "={{ $json.information_requirements }}"}, {"fieldId": "admin_fee", "fieldValue": "={{ $json.admin_fee }}"}, {"fieldId": "landlord_recapture_right", "fieldValue": "={{ $json.landlord_recapture_right }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [5280, -1580], "id": "9e7f2330-e0dc-40da-8eeb-6710530e0f39", "name": "Add Assignment Subletting Terms", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {"tableId": "default_remedies", "fieldsUi": {"fieldValues": [{"fieldId": "lease_id", "fieldValue": "={{ $('Create row in lease_documents table').first().json.lease_id }}"}, {"fieldId": "default_type", "fieldValue": "={{ $json.default_type }}"}, {"fieldId": "cure_period_days", "fieldValue": "={{ $json.cure_period_days }}"}, {"fieldId": "default_description", "fieldValue": "={{ $json.default_description }}"}, {"fieldId": "remedy_description", "fieldValue": "={{ $json.remedy_description }}"}, {"fieldId": "accelerated_rent_months", "fieldValue": "={{ $json.accelerated_rent_months }}"}, {"fieldId": "interest_calculation", "fieldValue": "={{ $json.interest_calculation }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [5280, -1300], "id": "d90e9282-307b-4e01-a593-fd81af6bbc1e", "name": "Add Default Remedies", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {"tableId": "insurance_liability_terms", "fieldsUi": {"fieldValues": [{"fieldId": "lease_id", "fieldValue": "={{ $('Create row in lease_documents table').first().json.lease_id }}"}, {"fieldId": "provision_type", "fieldValue": "={{ $json.provision_type }}"}, {"fieldId": "responsible_party", "fieldValue": "={{ $json.responsible_party }}"}, {"fieldId": "provision_description", "fieldValue": "={{ $json.provision_description }}"}, {"fieldId": "exclusions", "fieldValue": "={{ $json.exclusions }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [5280, -1060], "id": "ae698af3-7a73-4cac-8baf-c3c368caf586", "name": "Add Insurance Liability Terms", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {"tableId": "force_majeure_provisions", "fieldsUi": {"fieldValues": [{"fieldId": "lease_id", "fieldValue": "={{ $('Create row in lease_documents table').first().json.lease_id }}"}, {"fieldId": "event_type", "fieldValue": "={{ $json.event_type }}"}, {"fieldId": "event_description", "fieldValue": "={{ $json.event_description }}"}, {"fieldId": "effect_on_obligations", "fieldValue": "={{ $json.effect_on_obligations }}"}, {"fieldId": "rent_payment_exemption", "fieldValue": "={{ $json.rent_payment_exemption }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [5280, -860], "id": "c2c47e78-d609-479f-b497-04dad89c0c24", "name": "Add Force Majeure Provisions", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {"tableId": "destruction_expropriation_terms", "fieldsUi": {"fieldValues": [{"fieldId": "lease_id", "fieldValue": "={{ $('Create row in lease_documents table').first().json.lease_id }}"}, {"fieldId": "event_type", "fieldValue": "={{ $json.event_type }}"}, {"fieldId": "repair_timeline_days", "fieldValue": "={{ $json.repair_timeline_days }}"}, {"fieldId": "termination_rights", "fieldValue": "={{ $json.termination_rights }}"}, {"fieldId": "rent_abatement_provisions", "fieldValue": "={{ $json.rent_abatement_provisions }}"}, {"fieldId": "cost_responsibility", "fieldValue": "={{ $json.cost_responsibility }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [5280, -620], "id": "fbeb367b-53b4-48d6-b17e-71e5a237da62", "name": "Add Destruction Expropriation Terms", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {"tableId": "notice_provisions", "fieldsUi": {"fieldValues": [{"fieldId": "lease_id", "fieldValue": "={{ $('Create row in lease_documents table').first().json.lease_id }}"}, {"fieldId": "party_type", "fieldValue": "={{ $json.party_type }}"}, {"fieldId": "notice_address", "fieldValue": "={{ $json.notice_address }}"}, {"fieldId": "delivery_methods", "fieldValue": "={{ $json.delivery_methods }}"}, {"fieldId": "service_domicile", "fieldValue": "={{ $json.service_domicile }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [5260, -380], "id": "85f73c66-2c07-4bc3-9eab-2d3f63a38f53", "name": "Add Notice Provisions", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {"tableId": "legal_general_provisions", "fieldsUi": {"fieldValues": [{"fieldId": "lease_id", "fieldValue": "={{ $('Create row in lease_documents table').first().json.lease_id }}"}, {"fieldId": "provision_type", "fieldValue": "={{ $json.provision_type }}"}, {"fieldId": "provision_description", "fieldValue": "={{ $json.provision_description }}"}, {"fieldId": "jurisdiction", "fieldValue": "={{ $json.jurisdiction }}"}, {"fieldId": "requirements", "fieldValue": "={{ $json.requirements }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [6380, -1580], "id": "31087e64-1032-4db4-8033-8c09d8abbddf", "name": "Add Legal General Provisions", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {"tableId": "health_emergency_provisions", "fieldsUi": {"fieldValues": [{"fieldId": "lease_id", "fieldValue": "={{ $('Create row in lease_documents table').first().json.lease_id }}"}, {"fieldId": "provision_type", "fieldValue": "={{ $json.provision_type }}"}, {"fieldId": "provision_description", "fieldValue": "={{ $json.provision_description }}"}, {"fieldId": "landlord_rights", "fieldValue": "={{ $json.landlord_rights }}"}, {"fieldId": "liability_exclusions", "fieldValue": "={{ $json.liability_exclusions }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [6380, -1300], "id": "1fcc28ff-a55a-4ff7-a773-2a52e0f6df25", "name": "Add Health Emergency Provisions", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {"tableId": "signatures", "fieldsUi": {"fieldValues": [{"fieldId": "lease_id", "fieldValue": "={{ $('Create row in lease_documents table').first().json.lease_id }}"}, {"fieldId": "party_type", "fieldValue": "={{ $json.party_type }}"}, {"fieldId": "signatory_name", "fieldValue": "={{ $json.signatory_name }}"}, {"fieldId": "signatory_title", "fieldValue": "={{ $json.signatory_title }}"}, {"fieldId": "execution_date", "fieldValue": "={{ $json.execution_date }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [6360, -1060], "id": "e80c3e5f-217d-4b9f-9733-e1876feeee61", "name": "Add Signatures", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {"tableId": "document_attachments", "fieldsUi": {"fieldValues": [{"fieldId": "lease_id", "fieldValue": "={{ $('Create row in lease_documents table').first().json.lease_id }}"}, {"fieldId": "attachment_type", "fieldValue": "={{ $json.attachment_type }}"}, {"fieldId": "attachment_name", "fieldValue": "={{ $json.attachment_name }}"}, {"fieldId": "attachment_description", "fieldValue": "={{ $json.attachment_description }}"}, {"fieldId": "annex_reference", "fieldValue": "={{ $json.annex_reference }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [6360, -820], "id": "753a277d-8f07-4868-9435-6d7cfcbf99b0", "name": "Add Document Attachments", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "attributes": {"attributes": [{"name": "party_name", "description": "name of the party", "required": true}, {"name": "legal_status", "description": "legal status of the party", "required": true}, {"name": "address_street", "description": "street address of the party"}, {"name": "address_city", "description": "city of the address of the party"}, {"name": "address_province", "description": "province of the address of the party"}, {"name": "address_postal_code", "description": "postal code of the address of the party"}, {"name": "representative_name", "description": "Name of the representative of the party"}, {"name": "representative_title", "description": "Title of the representative of the party"}, {"name": "authorization_details", "description": "Authorization details of the party"}, {"name": "email", "description": "the email of the party"}, {"name": "phone_number", "description": "the phone number of the party"}]}, "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value.\n\nDO IT FOR THE TENANT ONLY!"}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [1600, -2040], "id": "ee644901-a70b-43bf-8750-bd9e63276627", "name": "Parties: Tenant"}, {"parameters": {"workflowInputs": {"values": [{"name": "doc_id"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [100, -760], "id": "e85d86cf-d5e3-4388-9572-562fc60c89db", "name": "When Executed by Another Workflow"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "e0a0568a-8864-4e70-bea8-babc353a0374", "leftValue": "={{ $json.complete_content }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [500, -760], "id": "08fb223e-7a3f-457a-8406-e44822ff25c4", "name": "If"}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [740, -540], "id": "2cf3c9af-a08f-4c85-8be0-4cb5929ff4b1", "name": "No Operation, do nothing"}, {"parameters": {"fieldToSplitOut": "output.rent_escalations", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [6140, -600], "id": "3466b7c5-c4e2-4730-a524-085d5d322245", "name": "Split Out16"}, {"parameters": {"tableId": "rent_escalations", "fieldsUi": {"fieldValues": [{"fieldId": "lease_id", "fieldValue": "={{ $('Create row in lease_documents table').item.json.lease_id }}"}, {"fieldId": "escalation_type", "fieldValue": "={{ $json.escalation_type }}"}, {"fieldId": "start_date", "fieldValue": "={{ $json.start_date }}"}, {"fieldId": "end_date", "fieldValue": "={{ $json.end_date }}"}, {"fieldId": "amount_or_formula", "fieldValue": "={{ $json.amount_or_formula }}"}, {"fieldId": "frequency", "fieldValue": "={{ $json.frequency }}"}, {"fieldId": "review_mechanism", "fieldValue": "={{ $json.review_mechanism }}"}, {"fieldId": "annual_base_rent_for_period", "fieldValue": "={{ $json.annual_base_rent_for_period }}"}, {"fieldId": "monthly_base_rent_for_period", "fieldValue": "={{ $json.monthly_base_rent_for_period }}"}, {"fieldId": "rate_per_sqft_for_period", "fieldValue": "={{ $json.rate_per_sqft_for_period }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [6360, -600], "id": "db52895c-7605-4f9b-9b70-92b200c12f11", "name": "Add Rent Escalation", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {"fieldToSplitOut": "output.operating_costs", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [6140, -420], "id": "d6d7bf3a-4c87-461d-b17a-b98bb9ca616b", "name": "Split Out17"}, {"parameters": {}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [6560, -420], "id": "78efd71e-377c-48dc-bb92-034508338c27", "name": "Limit17"}, {"parameters": {"tableId": "operating_costs_details", "fieldsUi": {"fieldValues": [{"fieldId": "lease_id", "fieldValue": "={{ $('Create row in lease_documents table').item.json.lease_id }}"}, {"fieldId": "cost_type", "fieldValue": "={{ $json.cost_type }}"}, {"fieldId": "calculation_method", "fieldValue": "={{ $json.calculation_method }}"}, {"fieldId": "inclusions", "fieldValue": "={{ $json.inclusions }}"}, {"fieldId": "exclusions", "fieldValue": "={{ $json.exclusions }}"}, {"fieldId": "cap_type", "fieldValue": "={{ $json.cap_type }}"}, {"fieldId": "cap_amount_or_formula", "fieldValue": "={{ $json.cap_amount_or_formula }}"}, {"fieldId": "reconciliation_frequency", "fieldValue": "={{ $json.reconciliation_frequency }}"}, {"fieldId": "recovery_method", "fieldValue": "={{ $json.recovery_method }}"}, {"fieldId": "gross_up_provision_enabled", "fieldValue": "={{ $json.gross_up_provision_enabled }}"}, {"fieldId": "gross_up_occupancy_percentage", "fieldValue": "={{ $json.gross_up_occupancy_percentage }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [6360, -420], "id": "73f70e2b-bad2-413d-bef5-58cf1d95198c", "name": "Add Operating Costs", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {"fieldToSplitOut": "output.guarantees", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [2600, -2040], "id": "a336861e-720b-4ba4-b285-5450f00ad0da", "name": "Split Out18"}, {"parameters": {}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [2980, -2040], "id": "827d7ebf-2f36-46a3-bfe1-59defe6ebb81", "name": "Limit18"}, {"parameters": {"fieldToSplitOut": "output.guarantees", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [2600, -1820], "id": "baa89f29-8773-437c-b229-765748314df7", "name": "Split Out21"}, {"parameters": {}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [2980, -1820], "id": "e664444f-2767-4136-95b8-442f335e8949", "name": "Limit21"}, {"parameters": {"tableId": "guarantors", "fieldsUi": {"fieldValues": [{"fieldId": "lease_id", "fieldValue": "={{ $('Create row in lease_documents table').item.json.lease_id }}"}, {"fieldId": "party_id", "fieldValue": "={{ $('Add Tenant to Parties Table').item.json.party_id }}"}, {"fieldId": "guarantee_type", "fieldValue": "={{ $json.guarantee_type }}"}, {"fieldId": "max_liability_amount", "fieldValue": "={{ $json.max_liability_amount }}"}, {"fieldId": "guarantee_duration_start", "fieldValue": "={{ $json.guarantee_duration_start }}"}, {"fieldId": "guarantee_duration_end", "fieldValue": "={{ $json.guarantee_duration_end }}"}, {"fieldId": "conditions", "fieldValue": "={{ $json.conditions }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [2800, -2040], "id": "1b4388b7-857c-4a88-81e5-0e1748210f12", "name": "Add Guarantors Tenant", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {"tableId": "guarantors", "fieldsUi": {"fieldValues": [{"fieldId": "lease_id", "fieldValue": "={{ $('Create row in lease_documents table').item.json.lease_id }}"}, {"fieldId": "party_id", "fieldValue": "={{ $('Add Landlord to Parties Table').item.json.party_id }}"}, {"fieldId": "guarantee_type", "fieldValue": "={{ $json.guarantee_type }}"}, {"fieldId": "max_liability_amount", "fieldValue": "={{ $json.max_liability_amount }}"}, {"fieldId": "guarantee_duration_start", "fieldValue": "={{ $json.guarantee_duration_start }}"}, {"fieldId": "guarantee_duration_end", "fieldValue": "={{ $json.guarantee_duration_end }}"}, {"fieldId": "conditions", "fieldValue": "={{ $json.conditions }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [2800, -1820], "id": "0a78361d-7310-454e-9a1f-34f93a9fdc9f", "name": "Add Guarantors Landlord", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {"fieldToSplitOut": "output.termination_rights", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [3520, -2040], "id": "25b09a98-86bd-4f7e-a257-da063aa3da57", "name": "Split Out22"}, {"parameters": {}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [3880, -2040], "id": "7d50235e-1b3c-45ac-9ec8-d6c45b416875", "name": "Limit22"}, {"parameters": {"tableId": "early_termination_options", "fieldsUi": {"fieldValues": [{"fieldId": "lease_id", "fieldValue": "={{ $('Create row in lease_documents table').first().json.lease_id }}"}, {"fieldId": "party_id", "fieldValue": "={{ $('Add Tenant to Parties Table').first().json.party_id }}"}, {"fieldId": "termination_condition", "fieldValue": "={{ $json.termination_condition }}"}, {"fieldId": "notice_period_days", "fieldValue": "={{ $json.notice_period_days }}"}, {"fieldId": "penalty_amount_or_formula", "fieldValue": "={{ $json.penalty_amount_or_formula }}"}, {"fieldId": "effective_date_clause", "fieldValue": "={{ $json.effective_date_clause }}"}, {"fieldId": "conditions_for_exercise", "fieldValue": "={{ $json.conditions_for_exercise }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [3700, -2040], "id": "ab0ea43f-8446-48f9-a772-1db8ac292bd9", "name": "Add Early Termination Tenant", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {"fieldToSplitOut": "output.termination_rights", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [3520, -1820], "id": "2143cd5f-3512-4b7c-ae39-abed0ed0e3f0", "name": "Split Out23"}, {"parameters": {}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [3880, -1820], "id": "0d9bc114-e245-4e09-9b9a-555efaf6d627", "name": "Limit23"}, {"parameters": {"tableId": "early_termination_options", "fieldsUi": {"fieldValues": [{"fieldId": "lease_id", "fieldValue": "={{ $('Create row in lease_documents table').first().json.lease_id }}"}, {"fieldId": "party_id", "fieldValue": "={{ $('Add Landlord to Parties Table').first().json.party_id }}"}, {"fieldId": "termination_condition", "fieldValue": "={{ $json.termination_condition }}"}, {"fieldId": "notice_period_days", "fieldValue": "={{ $json.notice_period_days }}"}, {"fieldId": "penalty_amount_or_formula", "fieldValue": "={{ $json.penalty_amount_or_formula }}"}, {"fieldId": "effective_date_clause", "fieldValue": "={{ $json.effective_date_clause }}"}, {"fieldId": "conditions_for_exercise", "fieldValue": "={{ $json.conditions_for_exercise }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [3700, -1820], "id": "860ed859-5d53-4b37-bfd5-e3a292e2cfac", "name": "Add Early Termination Landlord", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {"fieldToSplitOut": "output.delivery_conditions", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [5040, -160], "id": "a19382a6-8b1c-4ae2-8a64-714290285264", "name": "Split Out19"}, {"parameters": {}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [5460, -160], "id": "abd4d8d0-43d8-403b-ae98-3f9d61ea9e4d", "name": "Limit19"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"rent_escalations\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"escalation_type\": {\n            \"type\": \"string\"\n          },\n          \"start_date\": {\n            \"type\": \"string\",\n            \"format\": \"date\"\n          },\n          \"end_date\": {\n            \"type\": \"string\",\n            \"format\": \"date\"\n          },\n          \"amount_or_formula\": {\n            \"type\": \"string\"\n          },\n          \"frequency\": {\n            \"type\": \"string\"\n          },\n          \"review_mechanism\": {\n            \"type\": \"string\"\n          },\n          \"annual_base_rent_for_period\": {\n            \"type\": \"number\"\n          },\n          \"monthly_base_rent_for_period\": {\n            \"type\": \"number\"\n          },\n          \"rate_per_sqft_for_period\": {\n            \"type\": \"number\"\n          }\n        },\n        \"required\": [\n          \"escalation_type\",\n          \"start_date\",\n          \"end_date\",\n          \"amount_or_formula\",\n          \"frequency\",\n          \"review_mechanism\",\n          \"annual_base_rent_for_period\",\n          \"monthly_base_rent_for_period\",\n          \"rate_per_sqft_for_period\"\n        ]\n      }\n    }\n  },\n  \"required\": [\"rent_escalations\"]\n}", "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [5780, -600], "id": "f7fb3f62-093b-486f-ab23-3e9167c7a4f3", "name": "Rent Escalation (Multiples)"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"operating_costs\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"cost_type\": {\n            \"type\": \"string\"\n          },\n          \"calculation_method\": {\n            \"type\": \"string\"\n          },\n          \"inclusions\": {\n            \"type\": \"string\"\n          },\n          \"exclusions\": {\n            \"type\": \"string\"\n          },\n          \"cap_type\": {\n            \"type\": \"string\"\n          },\n          \"cap_amount_or_formula\": {\n            \"type\": \"number\"\n          },\n          \"reconciliation_frequency\": {\n            \"type\": \"string\"\n          },\n          \"recovery_method\": {\n            \"type\": \"string\"\n          },\n          \"gross_up_provision_enabled\": {\n            \"type\": \"boolean\"\n          },\n          \"gross_up_occupancy_percentage\": {\n            \"type\": \"number\"\n          }\n        },\n        \"required\": [\n          \"cost_type\",\n          \"calculation_method\",\n          \"inclusions\",\n          \"exclusions\",\n          \"cap_type\",\n          \"cap_amount_or_formula\",\n          \"reconciliation_frequency\",\n          \"recovery_method\",\n          \"gross_up_provision_enabled\",\n          \"gross_up_occupancy_percentage\"\n        ]\n      }\n    }\n  },\n  \"required\": [\"operating_costs\"]\n}", "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [5780, -420], "id": "593180a4-35ca-48f8-8665-3bff28b4eefa", "name": "Operating Costs (Multiples)"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"termination_rights\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"termination_condition\": {\n            \"type\": \"string\"\n          },\n          \"notice_period_days\": {\n            \"type\": \"integer\"\n          },\n          \"penalty_amount_or_formula\": {\n            \"type\": \"string\"\n          },\n          \"effective_date_clause\": {\n            \"type\": \"string\"\n          },\n          \"conditions_for_exercise\": {\n            \"type\": \"string\"\n          }\n        },\n        \"required\": [\n          \"termination_condition\",\n          \"notice_period_days\",\n          \"penalty_amount_or_formula\",\n          \"effective_date_clause\",\n          \"conditions_for_exercise\"\n        ]\n      }\n    }\n  },\n  \"required\": [\"termination_rights\"]\n}", "options": {"systemPromptTemplate": "=You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value.\n\nExtract the information of the early termination options for the landlord specifically."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [3180, -1820], "id": "8e4c8b42-be6b-4cc6-b1e1-fbf6e98e25b4", "name": "Early Termination Landlord (Multiples)"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"termination_rights\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"termination_condition\": {\n            \"type\": \"string\"\n          },\n          \"notice_period_days\": {\n            \"type\": \"integer\"\n          },\n          \"penalty_amount_or_formula\": {\n            \"type\": \"string\"\n          },\n          \"effective_date_clause\": {\n            \"type\": \"string\"\n          },\n          \"conditions_for_exercise\": {\n            \"type\": \"string\"\n          }\n        },\n        \"required\": [\n          \"termination_condition\",\n          \"notice_period_days\",\n          \"penalty_amount_or_formula\",\n          \"effective_date_clause\",\n          \"conditions_for_exercise\"\n        ]\n      }\n    }\n  },\n  \"required\": [\"termination_rights\"]\n}", "options": {"systemPromptTemplate": "=You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value.\n\nExtract the information of the early termination options for the tenant specifically."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [3180, -2040], "id": "e7419e54-8033-4b9b-bd46-b867b4ec3962", "name": "Early Termination Tenant (Multiples)"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"guarantees\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"guarantee_type\": {\n            \"type\": \"string\"\n          },\n          \"max_liability_amount\": {\n            \"type\": \"number\"\n          },\n          \"guarantee_duration_start\": {\n            \"type\": \"string\",\n            \"format\": \"date\"\n          },\n          \"guarantee_duration_end\": {\n            \"type\": \"string\",\n            \"format\": \"date\"\n          },\n          \"conditions\": {\n            \"type\": \"string\"\n          }\n        },\n        \"required\": [\n          \"guarantee_type\",\n          \"max_liability_amount\",\n          \"guarantee_duration_start\",\n          \"guarantee_duration_end\",\n          \"conditions\"\n        ]\n      }\n    }\n  },\n  \"required\": [\"guarantees\"]\n}", "options": {"systemPromptTemplate": "=You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value.\n\nExtract the information of the guarantees for the tenant specifically."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [2240, -2040], "id": "b60da1ac-b36c-466f-bdca-0525e78abafc", "name": "Guarant<PERSON> (Multiples)"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"guarantees\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"guarantee_type\": {\n            \"type\": \"string\"\n          },\n          \"max_liability_amount\": {\n            \"type\": \"number\"\n          },\n          \"guarantee_duration_start\": {\n            \"type\": \"string\",\n            \"format\": \"date\"\n          },\n          \"guarantee_duration_end\": {\n            \"type\": \"string\",\n            \"format\": \"date\"\n          },\n          \"conditions\": {\n            \"type\": \"string\"\n          }\n        },\n        \"required\": [\n          \"guarantee_type\",\n          \"max_liability_amount\",\n          \"guarantee_duration_start\",\n          \"guarantee_duration_end\",\n          \"conditions\"\n        ]\n      }\n    }\n  },\n  \"required\": [\"guarantees\"]\n}", "options": {"systemPromptTemplate": "=You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value.\n\n\nExtract the information of the guarantees for the landlord specifically."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [2240, -1820], "id": "0ba104ee-028d-454d-a950-29810ccacc92", "name": "Guarantors Landlord (Multiples)"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"delivery_conditions\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"condition_type\": {\n            \"type\": \"string\"\n          },\n          \"description\": {\n            \"type\": \"string\"\n          },\n          \"landlord_obligations\": {\n            \"type\": \"string\"\n          },\n          \"tenant_obligations\": {\n            \"type\": \"string\"\n          },\n          \"make_good_requirements\": {\n            \"type\": \"string\"\n          },\n          \"delivery_condition_description\": {\n            \"type\": \"string\"\n          }\n        },\n        \"required\": [\n          \"condition_type\",\n          \"description\",\n          \"landlord_obligations\",\n          \"tenant_obligations\",\n          \"make_good_requirements\",\n          \"delivery_condition_description\"\n        ]\n      }\n    }\n  },\n  \"required\": [\"delivery_conditions\"]\n}", "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [4680, -160], "id": "fd05f61b-e6de-4356-a877-0d237bc20d29", "name": "Premises Condition Terms (Multiples)"}, {"parameters": {"tableId": "premises_condition_terms", "fieldsUi": {"fieldValues": [{"fieldId": "lease_id", "fieldValue": "={{ $('Create row in lease_documents table').item.json.lease_id }}"}, {"fieldId": "condition_type", "fieldValue": "={{ $json.condition_type }}"}, {"fieldId": "description", "fieldValue": "={{ $json.description }}"}, {"fieldId": "landlord_obligations", "fieldValue": "={{ $json.landlord_obligations }}"}, {"fieldId": "tenant_obligations", "fieldValue": "={{ $json.tenant_obligations }}"}, {"fieldId": "make_good_requirements", "fieldValue": "={{ $json.make_good_requirements }}"}, {"fieldId": "delivery_condition_description", "fieldValue": "={{ $json.delivery_condition_description }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [5260, -160], "id": "6e7bd880-1a60-449b-b848-8f2be55ed8f3", "name": "Add Premises Conditions Terms", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {"tableId": "dispute_resolution", "fieldsUi": {"fieldValues": [{"fieldId": "lease_id", "fieldValue": "={{ $('Create row in lease_documents table').item.json.lease_id }}"}, {"fieldId": "method_type", "fieldValue": "={{ $json.method_type }}"}, {"fieldId": "governing_law", "fieldValue": "={{ $json.governing_law }}"}, {"fieldId": "jurisdiction", "fieldValue": "={{ $json.jurisdiction }}"}, {"fieldId": "arbitration_rules", "fieldValue": "={{ $json.arbitration_rules }}"}, {"fieldId": "mediation_requirements", "fieldValue": "={{ $json.mediation_requirements }}"}, {"fieldId": "notice_period_before_action", "fieldValue": "={{ $json.notice_period_before_action }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [5260, 60], "id": "6a370f97-51e2-49ea-85e5-74f4ff8909ac", "name": "Add Dispute Resolution", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"dispute_resolution\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"method_type\": {\n            \"type\": \"string\"\n          },\n          \"governing_law\": {\n            \"type\": \"string\"\n          },\n          \"jurisdiction\": {\n            \"type\": \"string\"\n          },\n          \"arbitration_rules\": {\n            \"type\": \"string\"\n          },\n          \"mediation_requirements\": {\n            \"type\": \"string\"\n          },\n          \"notice_period_before_action\": {\n            \"type\": \"integer\"\n          }\n        },\n        \"required\": [\n          \"method_type\",\n          \"governing_law\",\n          \"jurisdiction\",\n          \"arbitration_rules\",\n          \"mediation_requirements\",\n          \"notice_period_before_action\"\n        ]\n      }\n    }\n  },\n  \"required\": [\"dispute_resolution\"]\n}", "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [4680, 60], "id": "f10db355-5d1e-4fc4-b50d-b43cf68c00a9", "name": "Dispute Resolution (Multiples)"}, {"parameters": {}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [5460, 60], "id": "7ae21159-abee-4f92-add7-9746193d79b0", "name": "Limit20"}, {"parameters": {"fieldToSplitOut": "output.dispute_resolution", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [5040, 60], "id": "cb476283-fef9-4d6c-a4d1-ca749a9d5298", "name": "Split Out20"}, {"parameters": {}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [5480, 240], "id": "7cabf2da-b98f-4908-b7b0-84eb0ef99f06", "name": "Limit24"}, {"parameters": {"fieldToSplitOut": "output.amenities", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [5060, 240], "id": "bfd682ee-c326-4d2c-9b52-86716936f4ea", "name": "Split Out24"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"amenities\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"amenity_type\": {\n            \"type\": \"string\"\n          },\n          \"quantity\": {\n            \"type\": \"integer\"\n          },\n          \"location_description\": {\n            \"type\": \"string\"\n          },\n          \"cost_per_unit\": {\n            \"type\": \"number\"\n          },\n          \"exclusivity\": {\n            \"type\": \"boolean\"\n          },\n          \"cost_per_unit_frequency\": {\n            \"type\": \"string\"\n          },\n          \"cost_escalation_terms\": {\n            \"type\": \"string\"\n          },\n          \"termination_terms\": {\n            \"type\": \"string\"\n          }\n        },\n        \"required\": [\n          \"amenity_type\",\n          \"quantity\",\n          \"location_description\",\n          \"cost_per_unit\",\n          \"exclusivity\",\n          \"cost_per_unit_frequency\",\n          \"cost_escalation_terms\",\n          \"termination_terms\"\n        ]\n      }\n    }\n  },\n  \"required\": [\"amenities\"]\n}", "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [4700, 240], "id": "dc946cb6-f761-4ef3-88f4-f78d25f30b85", "name": "Leased Amenities (Multiples)"}, {"parameters": {"tableId": "leased_amenities", "fieldsUi": {"fieldValues": [{"fieldId": "lease_id", "fieldValue": "={{ $('Create row in lease_documents table').item.json.lease_id }}"}, {"fieldId": "amenity_type", "fieldValue": "={{ $json.amenity_type }}"}, {"fieldId": "quantity", "fieldValue": "={{ $json.quantity }}"}, {"fieldId": "location_description", "fieldValue": "={{ $json.location_description }}"}, {"fieldId": "cost_per_unit", "fieldValue": "={{ $json.cost_per_unit }}"}, {"fieldId": "exclusivity", "fieldValue": "={{ $json.exclusivity }}"}, {"fieldId": "cost_per_unit_frequency", "fieldValue": "={{ $json.cost_per_unit_frequency }}"}, {"fieldId": "cost_escalation_terms", "fieldValue": "={{ $json.cost_escalation_terms }}"}, {"fieldId": "termination_terms", "fieldValue": "={{ $json.termination_terms }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [5280, 240], "id": "98cf6780-dd0e-4493-82a5-46b193d65544", "name": "Add Leased Amenities", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [7360, -600], "id": "af97fdf5-edf9-4fb0-b554-1255d50e065c", "name": "Limit25"}, {"parameters": {"fieldToSplitOut": "output.renewal_options", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [6940, -600], "id": "ee6b1c71-4525-4d76-bffc-8f0340529b8f", "name": "Split Out25"}, {"parameters": {}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [5480, 420], "id": "8b23dc14-6e22-4c3f-9e86-908f172df495", "name": "Limit26"}, {"parameters": {"fieldToSplitOut": "output.purchase_options", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [5060, 420], "id": "00b6fd92-4ea4-4c6b-aff6-66aa6b561900", "name": "Split Out26"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"renewal_options\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"option_number\": {\n            \"type\": \"integer\"\n          },\n          \"renewal_term_duration\": {\n            \"type\": \"string\"\n          },\n          \"rent_calculation_method\": {\n            \"type\": \"string\"\n          },\n          \"notice_period_days\": {\n            \"type\": \"integer\"\n          },\n          \"conditions_for_exercise\": {\n            \"type\": \"string\"\n          },\n          \"rent_review_mechanism_details\": {\n            \"type\": \"string\"\n          },\n          \"conditions_precedent_details\": {\n            \"type\": \"string\"\n          },\n          \"personal_right_clause\": {\n            \"type\": \"boolean\"\n          },\n          \"excluded_inducements_on_renewal\": {\n            \"type\": \"string\"\n          }\n        },\n        \"required\": [\n          \"option_number\",\n          \"renewal_term_duration\",\n          \"rent_calculation_method\",\n          \"notice_period_days\",\n          \"conditions_for_exercise\",\n          \"rent_review_mechanism_details\",\n          \"conditions_precedent_details\",\n          \"personal_right_clause\",\n          \"excluded_inducements_on_renewal\"\n        ]\n      }\n    }\n  },\n  \"required\": [\"renewal_options\"]\n}", "options": {"systemPromptTemplate": "=You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value.\n\nDO IT FOR THIS RENT ESCALATION:\n- Rent escalation ID: {{ $json.id }}\n- Escalation type: {{ $json.escalation_type }}\n- Start date: {{ $json.start_date }}\n- End date: {{ $json.end_date }}\n- Amount or formula: {{ $json.amount_or_formula }}\n- Frequency: {{ $json.frequency }}\n- Review Mechanism: {{ $json.review_mechanism }}\n- Annual base rent: {{ $json.annual_base_rent_for_period }}\n- Monthly Base Rent: {{ $json.monthly_base_rent_for_period }}\n- Rate per sqft for period: {{ $json.rate_per_sqft_for_period }}\n"}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [6580, -600], "id": "141f63f5-7498-46c0-bbdc-9036e4db21ec", "name": "Renewal Options (Multiples)"}, {"parameters": {"tableId": "renewal_options", "fieldsUi": {"fieldValues": [{"fieldId": "lease_id", "fieldValue": "={{ $('Create row in lease_documents table').item.json.lease_id }}"}, {"fieldId": "renewal_rent_escalation_id", "fieldValue": "={{ $('Add Rent Escalation').item.json.id }}"}, {"fieldId": "option_number", "fieldValue": "={{ $json.option_number }}"}, {"fieldId": "renewal_term_duration", "fieldValue": "={{ $json.renewal_term_duration }}"}, {"fieldId": "rent_calculation_method", "fieldValue": "={{ $json.rent_calculation_method }}"}, {"fieldId": "notice_period_days", "fieldValue": "={{ $json.notice_period_days }}"}, {"fieldId": "conditions_for_exercise", "fieldValue": "={{ $json.conditions_for_exercise }}"}, {"fieldId": "rent_review_mechanism_details", "fieldValue": "={{ $json.rent_review_mechanism_details }}"}, {"fieldId": "conditions_precedent_details", "fieldValue": "={{ $json.conditions_precedent_details }}"}, {"fieldId": "personal_right_clause", "fieldValue": "={{ $json.personal_right_clause }}"}, {"fieldId": "excluded_inducements_on_renewal", "fieldValue": "={{ $json.excluded_inducements_on_renewal }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [7160, -600], "id": "4c6b0762-95b9-456d-b7f5-41ffdc564231", "name": "Add Renewal Options", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {"tableId": "purchase_options", "fieldsUi": {"fieldValues": [{"fieldId": "lease_id", "fieldValue": "={{ $('Create row in lease_documents table').item.json.lease_id }}"}, {"fieldId": "option_type", "fieldValue": "={{ $json.option_type }}"}, {"fieldId": "conditions", "fieldValue": "={{ $json.conditions }}"}, {"fieldId": "purchase_price_formula", "fieldValue": "={{ $json.purchase_price_formula }}"}, {"fieldId": "exercise_period", "fieldValue": "={{ $json.exercise_period }}"}, {"fieldId": "notice_requirements", "fieldValue": "={{ $json.notice_requirements }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [5280, 420], "id": "dda7fcc2-8fdf-46b1-8446-114a749cbed7", "name": "Add Purchase Options", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"purchase_options\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"option_type\": {\n            \"type\": \"string\"\n          },\n          \"conditions\": {\n            \"type\": \"string\"\n          },\n          \"purchase_price_formula\": {\n            \"type\": \"string\"\n          },\n          \"exercise_period\": {\n            \"type\": \"string\"\n          },\n          \"notice_requirements\": {\n            \"type\": \"string\"\n          }\n        },\n        \"required\": [\n          \"option_type\",\n          \"conditions\",\n          \"purchase_price_formula\",\n          \"exercise_period\",\n          \"notice_requirements\"\n        ]\n      }\n    }\n  },\n  \"required\": [\"purchase_options\"]\n}", "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [4700, 420], "id": "a37fa543-8c1a-4fbc-b291-232e17e66709", "name": "Purchase Options (Multiples)"}, {"parameters": {}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [5480, 700], "id": "6aae144c-13e3-4c4b-93cf-728afde1f0ab", "name": "Limit27"}, {"parameters": {"fieldToSplitOut": "output.environmental_provisions", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [5060, 700], "id": "635fcd21-e233-4272-9bd3-f826117bcfcb", "name": "Split Out27"}, {"parameters": {}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [5440, 1140], "id": "eff963dd-6100-402e-924b-6b347111ecfa", "name": "Limit28"}, {"parameters": {"fieldToSplitOut": "output.tenant_inducements", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [5060, 920], "id": "52378286-4d1d-4098-901e-2a7edf2aa9a2", "name": "Split Out28"}, {"parameters": {}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [5460, 920], "id": "67fe0a86-9794-4b1a-9e76-8452e567cd56", "name": "Limit29"}, {"parameters": {"fieldToSplitOut": "output.brokers", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [5040, 1140], "id": "7ce4090a-fd9e-4ca6-871f-80e0cfeabfe5", "name": "Split Out29"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"environmental_provisions\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"provision_type\": {\n            \"type\": \"string\"\n          },\n          \"description\": {\n            \"type\": \"string\"\n          },\n          \"responsible_party\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"Tenant\",\n              \"Landlord\"\n            ]\n          },\n          \"indemnification_terms\": {\n            \"type\": \"string\"\n          }\n        },\n        \"required\": [\n          \"provision_type\",\n          \"description\",\n          \"responsible_party\",\n          \"indemnification_terms\"\n        ]\n      }\n    }\n  },\n  \"required\": [\"environmental_provisions\"]\n}", "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [4700, 700], "id": "aae273f3-fd9b-4573-bbd1-66a760507bb2", "name": "Environmental Provisions (Multiples)"}, {"parameters": {"tableId": "environmental_provisions", "fieldsUi": {"fieldValues": [{"fieldId": "lease_id", "fieldValue": "={{ $('Create row in lease_documents table').item.json.lease_id }}"}, {"fieldId": "provision_type", "fieldValue": "={{ $json.provision_type }}"}, {"fieldId": "description", "fieldValue": "={{ $json.description }}"}, {"fieldId": "responsible_party", "fieldValue": "={{ $json.responsible_party }}"}, {"fieldId": "indemnification_terms", "fieldValue": "={{ $json.indemnification_terms }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [5280, 700], "id": "267b10e0-9591-4acc-9926-6cb906e35212", "name": "Add Environmental Provisions", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"tenant_inducements\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"inducement_type\": {\n            \"type\": \"string\"\n          },\n          \"amount\": {\n            \"type\": \"number\"\n          },\n          \"conditions\": {\n            \"type\": \"string\"\n          },\n          \"payment_schedule\": {\n            \"type\": \"string\"\n          },\n          \"rent_free_period_start\": {\n            \"type\": \"string\",\n            \"format\": \"date\"\n          },\n          \"rent_free_period_end\": {\n            \"type\": \"string\",\n            \"format\": \"date\"\n          }\n        },\n        \"required\": [\n          \"inducement_type\",\n          \"amount\",\n          \"conditions\",\n          \"payment_schedule\",\n          \"rent_free_period_start\",\n          \"rent_free_period_end\"\n        ]\n      }\n    }\n  },\n  \"required\": [\"tenant_inducements\"]\n}", "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [4700, 920], "id": "369a9cc8-2b9b-47d6-aa96-89b59a69cdb4", "name": "Tenant Inducements (Multiples)"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"brokers\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"broker_name\": {\n            \"type\": \"string\"\n          },\n          \"company_name\": {\n            \"type\": \"string\"\n          },\n          \"broker_type\": {\n            \"type\": \"string\"\n          },\n          \"commission_details\": {\n            \"type\": \"string\"\n          }\n        },\n        \"required\": [\n          \"broker_name\",\n          \"company_name\",\n          \"broker_type\",\n          \"commission_details\"\n        ]\n      }\n    }\n  },\n  \"required\": [\"brokers\"]\n}", "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [4680, 1140], "id": "dcba0dc4-6498-45d7-88b4-d0874ddc9011", "name": "Brokers (Multiples)"}, {"parameters": {"tableId": "brokers", "fieldsUi": {"fieldValues": [{"fieldId": "lease_id", "fieldValue": "={{ $('Create row in lease_documents table').item.json.lease_id }}"}, {"fieldId": "broker_name", "fieldValue": "={{ $json.broker_name }}"}, {"fieldId": "company_name", "fieldValue": "={{ $json.company_name }}"}, {"fieldId": "broker_type", "fieldValue": "={{ $json.broker_type }}"}, {"fieldId": "commission_details", "fieldValue": "={{ $json.commission_details }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [5240, 1140], "id": "6b12f0e3-55a7-4c53-9927-f280152930d1", "name": "Add Brokers", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {"tableId": "tenant_inducements", "fieldsUi": {"fieldValues": [{"fieldId": "lease_id", "fieldValue": "={{ $('Create row in lease_documents table').item.json.lease_id }}"}, {"fieldId": "inducement_type", "fieldValue": "={{ $json.inducement_type }}"}, {"fieldId": "amount", "fieldValue": "={{ $json.amount }}"}, {"fieldId": "conditions", "fieldValue": "={{ $json.conditions }}"}, {"fieldId": "payment_schedule", "fieldValue": "={{ $json.payment_schedule }}"}, {"fieldId": "rent_free_period_start", "fieldValue": "={{ $json.rent_free_period_start }}"}, {"fieldId": "rent_free_period_end", "fieldValue": "={{ $json.rent_free_period_end }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [5260, 920], "id": "dad78fee-c89d-42b1-a0db-e3a9f471b81f", "name": "Add Tenant Inducements", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [5440, 1380], "id": "d908b90c-b519-4c5e-9ef1-e5b143a62e76", "name": "Limit30"}, {"parameters": {"fieldToSplitOut": "output.representation_warranties", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [5040, 1380], "id": "8ff45962-81e2-4b75-a18d-76e9be003b19", "name": "Split Out30"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"representation_warranties\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"party_making_representation\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"Tenant\",\n              \"Landlord\"\n            ]\n          },\n          \"description\": {\n            \"type\": \"string\"\n          },\n          \"indemnification_terms\": {\n            \"type\": \"string\"\n          }\n        },\n        \"required\": [\n          \"party_making_representation\",\n          \"description\",\n          \"indemnification_terms\"\n        ]\n      }\n    }\n  },\n  \"required\": [\"representation_warranties\"]\n}", "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [4680, 1380], "id": "800e7db2-9997-4a88-832a-2c1e5255ac8c", "name": "Representations Warranties (Multiples)"}, {"parameters": {"tableId": "representations_warranties", "fieldsUi": {"fieldValues": [{"fieldId": "lease_id", "fieldValue": "={{ $('Create row in lease_documents table').item.json.lease_id }}"}, {"fieldId": "party_making_representation", "fieldValue": "={{ $json.party_making_representation }}"}, {"fieldId": "description", "fieldValue": "={{ $json.description }}"}, {"fieldId": "indemnification_terms", "fieldValue": "={{ $json.indemnification_terms }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [5240, 1380], "id": "25272d34-4448-45a9-a1e2-a40c24273ea8", "name": "Add Representation Warranties", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}], "connections": {"OpenRouter Chat Model": {"ai_languageModel": [[{"node": "Lease", "type": "ai_languageModel", "index": 0}, {"node": "Parties: Landlord", "type": "ai_languageModel", "index": 0}, {"node": "Parties: Tenant", "type": "ai_languageModel", "index": 0}, {"node": "Properties", "type": "ai_languageModel", "index": 0}, {"node": "Financial Terms", "type": "ai_languageModel", "index": 0}, {"node": "Security Deposits", "type": "ai_languageModel", "index": 0}, {"node": "Expiration Holdover Terms", "type": "ai_languageModel", "index": 0}, {"node": "Restrictions (Multiples)", "type": "ai_languageModel", "index": 0}, {"node": "Maintenance Obligations (Multiples)", "type": "ai_languageModel", "index": 0}, {"node": "Improvement Terms (Multiples)", "type": "ai_languageModel", "index": 0}, {"node": "Access Inspection Rights (Multiples)", "type": "ai_languageModel", "index": 0}, {"node": "Compliance Requirements (Multiples)", "type": "ai_languageModel", "index": 0}, {"node": "Signage Provisions (Multiples)", "type": "ai_languageModel", "index": 0}, {"node": "Assignment Subletting Terms (Multiples)", "type": "ai_languageModel", "index": 0}, {"node": "<PERSON><PERSON><PERSON> (Multiples)", "type": "ai_languageModel", "index": 0}, {"node": "Insurance Liability Terms (Multiples)", "type": "ai_languageModel", "index": 0}, {"node": "Force Majeure Provisions (Multiples)", "type": "ai_languageModel", "index": 0}, {"node": "Destruction Expropriation Terms (Multiples)", "type": "ai_languageModel", "index": 0}, {"node": "Notice Provisions (Multiples)", "type": "ai_languageModel", "index": 0}, {"node": "Legal General Provisions (Multiples)", "type": "ai_languageModel", "index": 0}, {"node": "Health Emergency Provisions (Multiples)", "type": "ai_languageModel", "index": 0}, {"node": "Signatures (Multiples)", "type": "ai_languageModel", "index": 0}, {"node": "Document Attachments (Multiples)", "type": "ai_languageModel", "index": 0}, {"node": "Rent Escalation (Multiples)", "type": "ai_languageModel", "index": 0}, {"node": "Operating Costs (Multiples)", "type": "ai_languageModel", "index": 0}, {"node": "Guarant<PERSON> (Multiples)", "type": "ai_languageModel", "index": 0}, {"node": "Guarantors Landlord (Multiples)", "type": "ai_languageModel", "index": 0}, {"node": "Early Termination Tenant (Multiples)", "type": "ai_languageModel", "index": 0}, {"node": "Early Termination Landlord (Multiples)", "type": "ai_languageModel", "index": 0}, {"node": "Premises Condition Terms (Multiples)", "type": "ai_languageModel", "index": 0}, {"node": "Dispute Resolution (Multiples)", "type": "ai_languageModel", "index": 0}, {"node": "Leased Amenities (Multiples)", "type": "ai_languageModel", "index": 0}, {"node": "Renewal Options (Multiples)", "type": "ai_languageModel", "index": 0}, {"node": "Purchase Options (Multiples)", "type": "ai_languageModel", "index": 0}, {"node": "Environmental Provisions (Multiples)", "type": "ai_languageModel", "index": 0}, {"node": "Tenant Inducements (Multiples)", "type": "ai_languageModel", "index": 0}, {"node": "Brokers (Multiples)", "type": "ai_languageModel", "index": 0}, {"node": "Representations Warranties (Multiples)", "type": "ai_languageModel", "index": 0}]]}, "Get Document Content": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Create row in lease_documents table": {"main": [[{"node": "Parties: Landlord", "type": "main", "index": 0}, {"node": "Parties: Tenant", "type": "main", "index": 0}, {"node": "Properties", "type": "main", "index": 0}, {"node": "Financial Terms", "type": "main", "index": 0}, {"node": "Security Deposits", "type": "main", "index": 0}, {"node": "Expiration Holdover Terms", "type": "main", "index": 0}, {"node": "Restrictions (Multiples)", "type": "main", "index": 0}, {"node": "Maintenance Obligations (Multiples)", "type": "main", "index": 0}, {"node": "Improvement Terms (Multiples)", "type": "main", "index": 0}, {"node": "Access Inspection Rights (Multiples)", "type": "main", "index": 0}, {"node": "Compliance Requirements (Multiples)", "type": "main", "index": 0}, {"node": "Signage Provisions (Multiples)", "type": "main", "index": 0}, {"node": "Assignment Subletting Terms (Multiples)", "type": "main", "index": 0}, {"node": "<PERSON><PERSON><PERSON> (Multiples)", "type": "main", "index": 0}, {"node": "Insurance Liability Terms (Multiples)", "type": "main", "index": 0}, {"node": "Force Majeure Provisions (Multiples)", "type": "main", "index": 0}, {"node": "Destruction Expropriation Terms (Multiples)", "type": "main", "index": 0}, {"node": "Notice Provisions (Multiples)", "type": "main", "index": 0}, {"node": "Legal General Provisions (Multiples)", "type": "main", "index": 0}, {"node": "Health Emergency Provisions (Multiples)", "type": "main", "index": 0}, {"node": "Signatures (Multiples)", "type": "main", "index": 0}, {"node": "Document Attachments (Multiples)", "type": "main", "index": 0}, {"node": "Rent Escalation (Multiples)", "type": "main", "index": 0}, {"node": "Operating Costs (Multiples)", "type": "main", "index": 0}, {"node": "Premises Condition Terms (Multiples)", "type": "main", "index": 0}, {"node": "Dispute Resolution (Multiples)", "type": "main", "index": 0}, {"node": "Leased Amenities (Multiples)", "type": "main", "index": 0}, {"node": "Purchase Options (Multiples)", "type": "main", "index": 0}, {"node": "Environmental Provisions (Multiples)", "type": "main", "index": 0}, {"node": "Tenant Inducements (Multiples)", "type": "main", "index": 0}, {"node": "Brokers (Multiples)", "type": "main", "index": 0}, {"node": "Representations Warranties (Multiples)", "type": "main", "index": 0}]]}, "Lease": {"main": [[{"node": "Create row in lease_documents table", "type": "main", "index": 0}]]}, "Parties: Landlord": {"main": [[{"node": "Add Landlord to Parties Table", "type": "main", "index": 0}]]}, "Add Landlord to Parties Table": {"main": [[{"node": "Guarantors Landlord (Multiples)", "type": "main", "index": 0}]]}, "Add Tenant to Parties Table": {"main": [[{"node": "Guarant<PERSON> (Multiples)", "type": "main", "index": 0}]]}, "Properties": {"main": [[{"node": "Add Property Details", "type": "main", "index": 0}]]}, "Add Property Details": {"main": [[]]}, "Financial Terms": {"main": [[{"node": "Add Financial Terms", "type": "main", "index": 0}]]}, "Add Financial Terms": {"main": [[]]}, "Security Deposits": {"main": [[{"node": "Add Security Depost", "type": "main", "index": 0}]]}, "Add Security Depost": {"main": [[]]}, "Expiration Holdover Terms": {"main": [[{"node": "Add Expiration Holdover Terms", "type": "main", "index": 0}]]}, "Add Expiration Holdover Terms": {"main": [[]]}, "Split Out": {"main": [[{"node": "Add Restriction", "type": "main", "index": 0}]]}, "Add Restriction": {"main": [[{"node": "Limit", "type": "main", "index": 0}]]}, "Limit": {"main": [[]]}, "Restrictions (Multiples)": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Maintenance Obligations (Multiples)": {"main": [[{"node": "Split Out1", "type": "main", "index": 0}]]}, "Add Maintenance Obligations": {"main": [[{"node": "Limit1", "type": "main", "index": 0}]]}, "Split Out1": {"main": [[{"node": "Add Maintenance Obligations", "type": "main", "index": 0}]]}, "Limit1": {"main": [[]]}, "Split Out2": {"main": [[{"node": "Add Improvement Terms", "type": "main", "index": 0}]]}, "Limit2": {"main": [[]]}, "Improvement Terms (Multiples)": {"main": [[{"node": "Split Out2", "type": "main", "index": 0}]]}, "Add Improvement Terms": {"main": [[{"node": "Limit2", "type": "main", "index": 0}]]}, "Split Out3": {"main": [[{"node": "Add Access Inspection Rights", "type": "main", "index": 0}]]}, "Limit3": {"main": [[]]}, "Access Inspection Rights (Multiples)": {"main": [[{"node": "Split Out3", "type": "main", "index": 0}]]}, "Add Access Inspection Rights": {"main": [[{"node": "Limit3", "type": "main", "index": 0}]]}, "Split Out4": {"main": [[{"node": "Add Compliance Requirements", "type": "main", "index": 0}]]}, "Limit4": {"main": [[]]}, "Split Out5": {"main": [[{"node": "Add Signage Provisions", "type": "main", "index": 0}]]}, "Limit5": {"main": [[]]}, "Split Out6": {"main": [[{"node": "Add Assignment Subletting Terms", "type": "main", "index": 0}]]}, "Limit6": {"main": [[]]}, "Split Out7": {"main": [[{"node": "Add Default Remedies", "type": "main", "index": 0}]]}, "Limit7": {"main": [[]]}, "Split Out8": {"main": [[{"node": "Add Insurance Liability Terms", "type": "main", "index": 0}]]}, "Limit8": {"main": [[]]}, "Compliance Requirements (Multiples)": {"main": [[{"node": "Split Out4", "type": "main", "index": 0}]]}, "Signage Provisions (Multiples)": {"main": [[{"node": "Split Out5", "type": "main", "index": 0}]]}, "Assignment Subletting Terms (Multiples)": {"main": [[{"node": "Split Out6", "type": "main", "index": 0}]]}, "Default Remedies (Multiples)": {"main": [[{"node": "Split Out7", "type": "main", "index": 0}]]}, "Split Out9": {"main": [[{"node": "Add Force Majeure Provisions", "type": "main", "index": 0}]]}, "Limit9": {"main": [[]]}, "Split Out10": {"main": [[{"node": "Add Destruction Expropriation Terms", "type": "main", "index": 0}]]}, "Limit10": {"main": [[]]}, "Split Out11": {"main": [[{"node": "Add Notice Provisions", "type": "main", "index": 0}]]}, "Limit11": {"main": [[]]}, "Split Out12": {"main": [[{"node": "Add Legal General Provisions", "type": "main", "index": 0}]]}, "Limit12": {"main": [[]]}, "Insurance Liability Terms (Multiples)": {"main": [[{"node": "Split Out8", "type": "main", "index": 0}]]}, "Force Majeure Provisions (Multiples)": {"main": [[{"node": "Split Out9", "type": "main", "index": 0}]]}, "Destruction Expropriation Terms (Multiples)": {"main": [[{"node": "Split Out10", "type": "main", "index": 0}]]}, "Notice Provisions (Multiples)": {"main": [[{"node": "Split Out11", "type": "main", "index": 0}]]}, "Legal General Provisions (Multiples)": {"main": [[{"node": "Split Out12", "type": "main", "index": 0}]]}, "Split Out13": {"main": [[{"node": "Add Health Emergency Provisions", "type": "main", "index": 0}]]}, "Limit13": {"main": [[]]}, "Split Out14": {"main": [[{"node": "Add Signatures", "type": "main", "index": 0}]]}, "Limit14": {"main": [[]]}, "Split Out15": {"main": [[{"node": "Add Document Attachments", "type": "main", "index": 0}]]}, "Health Emergency Provisions (Multiples)": {"main": [[{"node": "Split Out13", "type": "main", "index": 0}]]}, "Signatures (Multiples)": {"main": [[{"node": "Split Out14", "type": "main", "index": 0}]]}, "Document Attachments (Multiples)": {"main": [[{"node": "Split Out15", "type": "main", "index": 0}]]}, "Add Compliance Requirements": {"main": [[{"node": "Limit4", "type": "main", "index": 0}]]}, "Add Signage Provisions": {"main": [[{"node": "Limit5", "type": "main", "index": 0}]]}, "Add Assignment Subletting Terms": {"main": [[{"node": "Limit6", "type": "main", "index": 0}]]}, "Add Default Remedies": {"main": [[{"node": "Limit7", "type": "main", "index": 0}]]}, "Add Insurance Liability Terms": {"main": [[{"node": "Limit8", "type": "main", "index": 0}]]}, "Add Force Majeure Provisions": {"main": [[{"node": "Limit9", "type": "main", "index": 0}]]}, "Add Destruction Expropriation Terms": {"main": [[{"node": "Limit10", "type": "main", "index": 0}]]}, "Add Notice Provisions": {"main": [[{"node": "Limit11", "type": "main", "index": 0}]]}, "Add Legal General Provisions": {"main": [[{"node": "Limit12", "type": "main", "index": 0}]]}, "Add Health Emergency Provisions": {"main": [[{"node": "Limit13", "type": "main", "index": 0}]]}, "Add Signatures": {"main": [[{"node": "Limit14", "type": "main", "index": 0}]]}, "Add Document Attachments": {"main": [[{"node": "Limit15", "type": "main", "index": 0}]]}, "Parties: Tenant": {"main": [[{"node": "Add Tenant to Parties Table", "type": "main", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Get Document Content", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Lease", "type": "main", "index": 0}], [{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}, "Split Out16": {"main": [[{"node": "Add Rent Escalation", "type": "main", "index": 0}]]}, "Add Rent Escalation": {"main": [[{"node": "Renewal Options (Multiples)", "type": "main", "index": 0}]]}, "Split Out17": {"main": [[{"node": "Add Operating Costs", "type": "main", "index": 0}]]}, "Add Operating Costs": {"main": [[{"node": "Limit17", "type": "main", "index": 0}]]}, "Split Out18": {"main": [[{"node": "Add Guarantors Tenant", "type": "main", "index": 0}]]}, "Limit18": {"main": [[{"node": "Early Termination Tenant (Multiples)", "type": "main", "index": 0}]]}, "Split Out21": {"main": [[{"node": "Add Guarantors Landlord", "type": "main", "index": 0}]]}, "Limit21": {"main": [[{"node": "Early Termination Landlord (Multiples)", "type": "main", "index": 0}]]}, "Add Guarantors Tenant": {"main": [[{"node": "Limit18", "type": "main", "index": 0}]]}, "Add Guarantors Landlord": {"main": [[{"node": "Limit21", "type": "main", "index": 0}]]}, "Split Out22": {"main": [[{"node": "Add Early Termination Tenant", "type": "main", "index": 0}]]}, "Add Early Termination Tenant": {"main": [[{"node": "Limit22", "type": "main", "index": 0}]]}, "Split Out23": {"main": [[{"node": "Add Early Termination Landlord", "type": "main", "index": 0}]]}, "Add Early Termination Landlord": {"main": [[{"node": "Limit23", "type": "main", "index": 0}]]}, "Split Out19": {"main": [[{"node": "Add Premises Conditions Terms", "type": "main", "index": 0}]]}, "Rent Escalation (Multiples)": {"main": [[{"node": "Split Out16", "type": "main", "index": 0}]]}, "Operating Costs (Multiples)": {"main": [[{"node": "Split Out17", "type": "main", "index": 0}]]}, "Early Termination Landlord (Multiples)": {"main": [[{"node": "Split Out23", "type": "main", "index": 0}]]}, "Early Termination Tenant (Multiples)": {"main": [[{"node": "Split Out22", "type": "main", "index": 0}]]}, "Guarantors Tenant (Multiples)": {"main": [[{"node": "Split Out18", "type": "main", "index": 0}]]}, "Guarantors Landlord (Multiples)": {"main": [[{"node": "Split Out21", "type": "main", "index": 0}]]}, "Premises Condition Terms (Multiples)": {"main": [[{"node": "Split Out19", "type": "main", "index": 0}]]}, "Add Premises Conditions Terms": {"main": [[{"node": "Limit19", "type": "main", "index": 0}]]}, "Add Dispute Resolution": {"main": [[{"node": "Limit20", "type": "main", "index": 0}]]}, "Dispute Resolution (Multiples)": {"main": [[{"node": "Split Out20", "type": "main", "index": 0}]]}, "Split Out20": {"main": [[{"node": "Add Dispute Resolution", "type": "main", "index": 0}]]}, "Split Out24": {"main": [[{"node": "Add Leased Amenities", "type": "main", "index": 0}]]}, "Leased Amenities (Multiples)": {"main": [[{"node": "Split Out24", "type": "main", "index": 0}]]}, "Add Leased Amenities": {"main": [[{"node": "Limit24", "type": "main", "index": 0}]]}, "Split Out25": {"main": [[{"node": "Add Renewal Options", "type": "main", "index": 0}]]}, "Split Out26": {"main": [[{"node": "Add Purchase Options", "type": "main", "index": 0}]]}, "Renewal Options (Multiples)": {"main": [[{"node": "Split Out25", "type": "main", "index": 0}]]}, "Add Renewal Options": {"main": [[{"node": "Limit25", "type": "main", "index": 0}]]}, "Add Purchase Options": {"main": [[{"node": "Limit26", "type": "main", "index": 0}]]}, "Purchase Options (Multiples)": {"main": [[{"node": "Split Out26", "type": "main", "index": 0}]]}, "Split Out27": {"main": [[{"node": "Add Environmental Provisions", "type": "main", "index": 0}]]}, "Split Out28": {"main": [[{"node": "Add Tenant Inducements", "type": "main", "index": 0}]]}, "Split Out29": {"main": [[{"node": "Add Brokers", "type": "main", "index": 0}]]}, "Environmental Provisions (Multiples)": {"main": [[{"node": "Split Out27", "type": "main", "index": 0}]]}, "Add Environmental Provisions": {"main": [[{"node": "Limit27", "type": "main", "index": 0}]]}, "Tenant Inducements (Multiples)": {"main": [[{"node": "Split Out28", "type": "main", "index": 0}]]}, "Brokers (Multiples)": {"main": [[{"node": "Split Out29", "type": "main", "index": 0}]]}, "Add Brokers": {"main": [[{"node": "Limit28", "type": "main", "index": 0}]]}, "Add Tenant Inducements": {"main": [[{"node": "Limit29", "type": "main", "index": 0}]]}, "Split Out30": {"main": [[{"node": "Add Representation Warranties", "type": "main", "index": 0}]]}, "Representations Warranties (Multiples)": {"main": [[{"node": "Split Out30", "type": "main", "index": 0}]]}, "Add Representation Warranties": {"main": [[{"node": "Limit30", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "2faaf9ff911f9829d7f722a5ec919613e50308f0846f977c43de1cabcc499753"}}